import React from 'react';
import {
  <PERSON>,
  Mo<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Flex,
  Menu,
  Text,
  Indicator
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconClock2, IconInfoCircle } from '@tabler/icons-react';
import { MdOutlineAutoDelete, MdOutlineUpdate } from "react-icons/md";
import { FaRegRectangleList, <PERSON>a<PERSON>ser<PERSON>ie, FaUserDoctor } from "react-icons/fa6";
import { BsFillCalendar2DateFill, BsCardChecklist } from "react-icons/bs";
import { IoArrowForwardOutline } from "react-icons/io5";
import { HiDotsVertical } from "react-icons/hi";
import moment from 'moment';
import MenuIcons from '@/components/agenda/icons/MenuIcons';
import { Appointment, AppointmentEvent } from '@/types/typesCalendarPatient';

interface EventComponentProps {
  event: Appointment;
  changeEndValuelunch: string;
  isSidebarVisible: boolean;
  activeEvent_Ids: Set<number>;
  activeIds: number[];
  activeVisits: Appointment[];
  appointments: Appointment[];
  handleRemoveLunchEvent: (eventId: string, resourceId: number | undefined) => void;
  activateAppointment: (appointment: Appointment) => void;
  setActiveVisits: React.Dispatch<React.SetStateAction<Appointment[]>>;
  setAppointments: React.Dispatch<React.SetStateAction<Appointment[]>>;
  handleLastVisit: (appointment: Appointment) => void;
  handleEditClick: (appointment: Appointment) => void;
  rescheduleAppointment: (appointment: Appointment) => void;
  openedFichepatient: () => void;
  setSelectedEvent: React.Dispatch<React.SetStateAction<AppointmentEvent | null>>;
  setShowViewModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleAddAlert: (appointment: Appointment) => void;
  setAlertsOpened: React.Dispatch<React.SetStateAction<boolean>>;
  handleAddReminder: (appointment: Appointment) => void;
  removeAppointment: (appointment: Appointment) => void;
}

const EventComponent: React.FC<EventComponentProps> = ({
  event,
  changeEndValuelunch,
  isSidebarVisible,
  activeEvent_Ids,
  activeIds,
  activeVisits,
  appointments,
  handleRemoveLunchEvent,
  activateAppointment,
  setActiveVisits,
  setAppointments,
  handleLastVisit,
  handleEditClick,
  rescheduleAppointment,
  openedFichepatient,
  setSelectedEvent,
  setShowViewModal,
  handleAddAlert,
  setAlertsOpened,
  handleAddReminder,
  removeAppointment
}) => {
  const appointment = event;
  const icon = <IconInfoCircle />;
  
  const [lunchTimeDeleteOpened, { open: openedlunchTimeDelete, close: closelunchTimeDelete }] = useDisclosure(false);

  return (
    <>
      {appointment.lunchTime ? (
        <div style={{ backgroundColor: `${changeEndValuelunch}` }}>
          <Group className="flex items-center w-full pl-1 h-[26px]">
            <Group className={isSidebarVisible ? "w-[81%]" : "w-[91%]"}>
              <IconClock2 stroke={1.25} />
              <div className="font-bold">{appointment.title}</div>
            </Group>
            <MdOutlineAutoDelete 
              size={18} 
              className="hover:fill-[#ED0423]" 
              onClick={openedlunchTimeDelete} 
            />
            <Modal
              opened={lunchTimeDeleteOpened}
              onClose={closelunchTimeDelete}
              withCloseButton={false}
            >
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                Êtes-vous sûr de vouloir supprimer l&apos;heure du déjeuner ?
              </Alert>
              <Group justify="space-between" mt="md" mb="xs">
                <Button
                  variant="filled"
                  size="xs"
                  radius="lg"
                  color="red"
                  onClick={() => {
                    handleRemoveLunchEvent(appointment.id, appointment.resourceId);
                    closelunchTimeDelete();
                  }}
                >
                  Oui
                </Button>
                <Button
                  variant="filled"
                  size="xs"
                  radius="lg"
                  color="lime.4"
                  onClick={closelunchTimeDelete}
                >
                  Non
                </Button>
              </Group>
            </Modal>
          </Group>
        </div>
      ) : (
        <>
          <div 
            className="event-content" 
            style={{ 
              backgroundColor: event.currentEventColor || event.color 
            }}
          >
            <Group className="flex justify-between items-center w-full pt-.5 pl-1 h-[26px]">
              <Menu 
                shadow="md" 
                width={429} 
                position="bottom-start" 
                transitionProps={{ transition: 'rotate-right', duration: 150 }} 
                withArrow 
                arrowPosition="center"
              >
                <Menu.Target>
                  <div className="font-bold flex" onClick={(e) => e.stopPropagation()}>
                    <span className="ml-2">
                      <FaRegRectangleList size={16} />
                    </span>
                    <span className="mt-[1.2px] ml-2">
                      <p className="capitalize">{appointment.last_name} {appointment.first_name}</p>
                    </span>
                    <Group gap="xs" className={isSidebarVisible ? "ml-2" : "ml-20"}>
                      <span><IconClock2 size={16} /></span>
                      <span className="-ml-2">{appointment.consultationDuration} min</span>
                    </Group>
                  </div>
                </Menu.Target>
                <Menu.Dropdown 
                  onClick={(e) => e.stopPropagation()}
                  style={{ backgroundColor: appointment.color }}
                  className="left-[156px]"
                >
                  <Card shadow="sm" padding="lg" radius="md" withBorder>
                    <Menu.Divider />
                    <List size="sm" withPadding className="capitalize" type="ordered">
                      <List.Item
                        icon={
                          <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                            <FaUserTie color={"#3799CE"} />
                            <IoArrowForwardOutline size={16} color={appointment.color} />
                          </Flex>
                        }
                      >
                        {appointment.last_name} {appointment.first_name}
                      </List.Item>

                      <List.Item
                        icon={
                          <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                            <BsFillCalendar2DateFill color={"#3799CE"} />
                            <IoArrowForwardOutline size={16} color={appointment.color} />
                          </Flex>
                        }
                      >
                        {moment(appointment.start).format("DD/MM/YYYY")}
                      </List.Item>

                      <List.Item
                        icon={
                          <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                            <MdOutlineUpdate color={"#3799CE"} />
                            <IoArrowForwardOutline size={16} color={appointment.color} />
                          </Flex>
                        }
                      >
                        {moment(appointment.start).format("HH:mm")}- {moment(appointment.end).format("HH:mm")}
                      </List.Item>

                      <List.Item
                        icon={
                          <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                            <FaUserDoctor color={"#3799CE"} />
                            <IoArrowForwardOutline size={16} color={appointment.color} />
                          </Flex>
                        }
                      >
                        {appointment.docteur}
                      </List.Item>

                      <List.Item
                        icon={
                          <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                            <BsCardChecklist color={"#3799CE"} />
                            <IoArrowForwardOutline size={16} color={appointment.color} />
                          </Flex>
                        }
                      >
                        {appointment.typeConsultation}
                      </List.Item>
                    </List>
                    <Menu.Divider />
                  </Card>
                </Menu.Dropdown>
              </Menu>

              <Menu shadow="md" width={200} position="bottom-end">
                <Menu.Target>
                  <span
                    className={`event.color hover:${appointment.color} rounded-full p-1 ml-auto`}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Group>
                      <Indicator
                        processing
                        inline
                        size={10}
                        offset={0}
                        position="bottom-end"
                        color="red"
                        withBorder
                        className={appointment.lunchTime ? "hidden" : ""}
                        disabled={!activeEvent_Ids.has(Number(event.id)) || activeIds.includes(Number(event.id))}
                        mr={isSidebarVisible ? 40 : 65}
                      />
                      <HiDotsVertical size={20} />
                    </Group>
                  </span>
                </Menu.Target>
                <Menu.Dropdown onClick={(e) => e.stopPropagation()}>
                  <Menu.Item leftSection={<MenuIcons.sltVisit size={16} />}>
                    <Group className="flex justify-between items-center w-full pt-.5 pl-1">
                      SLT
                      <Button component="div" size="compact-xs" color="#15AABF">
                        {activeVisits.filter(visit => visit.resourceId === appointment.resourceId).length}/30
                      </Button>
                    </Group>
                  </Menu.Item>
                  <Menu.Divider />
                  
                  <Menu.Item
                    leftSection={<MenuIcons.activeVisit size={16} />}
                    onClick={() => {
                      if (appointment.isActive) {
                        setActiveVisits(activeVisits.filter(visit => visit.id !== appointment.id));
                        setAppointments(appointments.map(app =>
                          app.id === appointment.id ? { ...app, isActive: false } : app
                        ));
                      } else {
                        activateAppointment(appointment);
                      }
                    }}
                  >
                    <Text c={appointment.isActive ? 'red' : 'green'}>
                      {appointment.isActive ? 'Désactiver la visite' : 'Activer la visite'}
                    </Text>
                  </Menu.Item>

                  <Menu.Item 
                    leftSection={<MenuIcons.lastVisit size={16} />} 
                    onClick={() => handleLastVisit(appointment)}
                  >
                    Dernière visite
                  </Menu.Item>
                  <Menu.Divider />
                  
                  <Menu.Item
                    leftSection={<MenuIcons.editAppointment size={16} />}
                    onClick={() => handleEditClick(appointment)}
                  >
                    Modifier RDV
                  </Menu.Item>
                  
                  <Menu.Item 
                    leftSection={<MenuIcons.nextAppointment size={16} />} 
                    onClick={() => rescheduleAppointment(appointment)}
                  >
                    Prochain RDV
                  </Menu.Item>
                  <Menu.Divider />
                  
                  <Menu.Item 
                    leftSection={<MenuIcons.patientFile size={16} />} 
                    onClick={() => openedFichepatient()}
                  >
                    Fiche patient
                  </Menu.Item>
                  
                  <Menu.Item
                    leftSection={<MenuIcons.patientDetails size={16} />}
                    onClick={() => {
                      setSelectedEvent(appointment);
                      setShowViewModal(true);
                    }}
                  >
                    Détails patient
                  </Menu.Item>
                  <Menu.Divider />
                  
                  <Menu.Item 
                    leftSection={<MenuIcons.addAlert size={16} />}
                    onClick={() => {
                      handleAddAlert(appointment);
                      setAlertsOpened(true);
                    }}
                  >
                    Ajouter une alerte
                  </Menu.Item>
                  
                  <Menu.Item 
                    leftSection={<MenuIcons.addReminder size={16} />} 
                    onClick={() => handleAddReminder(appointment)}
                  >
                    Ajouter un rappel
                  </Menu.Item>
                  <Menu.Divider />
                  
                  <Menu.Item
                    leftSection={<MdOutlineAutoDelete size={16} />}
                    onClick={() => removeAppointment(appointment)}
                  >
                    Supprimer RDV
                  </Menu.Item>
                  <Menu.Divider />
                  
                  <Menu.Item color="red" leftSection={<MenuIcons.cancel size={16} color="red" />}>
                    Annuler
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </Group>
          </div>
        </>
      )}
    </>
  );
};

export default EventComponent;
