"use client";
import {
  useState,
} from "react";
import React from "react";
import {  But<PERSON>,Text, } from '@mantine/core';
import SelectJournee from "../SelectJournee";
import "moment/locale/fr";
interface Event {
  id: number;
  title: string;
  name  : string;
  prenom: string;
  birth_date: string;
  age: number;
  selectedOption: string;
  etatCivil: string;
  cin: string;
  adresse: string;
  phone_numbers: string;
  email: string;
  docteur: string;
  consultation: string;
  typeConsultation: string;
  start: Date;
  end: Date;
  type: EventType;
  clientName?: string;
  description?: string;
  color?: string;
  style?: { backgroundColor: string };
  disabled?: string;
  isDraggable?: boolean;
  isResizable?: boolean;
  tooltip: string;
  lunchTime?: boolean;
  profession?: string; // ✅ Ajout de profession
  entreprise?: string;
  ville?: string;
  codePostal?: string;
  isExpanded?: boolean;  // Add this property
  resourceId: number;
  Commentaire: string;
  Commentairelistedattente: string;
  checkedListedattente?: boolean;
  checkedAppelvideo?: boolean;
  checkedRappelSms?: boolean;
  checkedRappelEmail?: boolean;
  aganda: string;
  sociale: string;
  etatAganda:string;
// ----------------
first_name: string;
last_name: string;

}
interface CalendarHeaderProps {
  label: string;
  date: Date;
  onNavigate: (action: "TODAY" | "PREV" | "NEXT" | "DATE", newDate?: Date) => void;
  onViewChange?: (view: string) => void; // Facultatif si nécessaire
  onAddToCalendar?: (event: Event) => void;
}
type EventType = "visit" | "visitor-counter" | "completed" | "diagnosis";
const CalendarHeader: React.FC<CalendarHeaderProps> = ({ 
  label, 
  date, 
  onNavigate,
}) => {
  const [selectedDateJ, setSelectedDateJ] = useState(date);
  const handleDateChange = (newDate: Date) => {
    setSelectedDateJ(newDate);
    onNavigate("DATE", newDate);
  };
  return (
    <div className="rounded-t-lg bg-[#3799CE] p-1 text-[#ffffff] w-[956px]">
      <div className="card-title h-[38px] flex justify-between items-center">
      <div className="flex justify-start">
         {/* liste d'attente */}
       
        <Button variant="filled" size="sm" onClick={() => onNavigate("TODAY")} className=" rounded-md  text-[var(--text-tab)] ButtonHover" fz="xs">
          Aujourd’hui
        </Button>
      </div>
        {/* Navigation Buttons & Label */}
        <div className="flex items-center">
          <button className="btn-sm mt-1 mr-1" onClick={() => onNavigate("PREV")}> <i className="icon icon-chevron-left text-lg"></i></button>
          <Text fw={550} c={"var(--text-daisy-white)"}>{label}</Text>
          <button className="btn-sm mt-1 ml-1" onClick={() => onNavigate("NEXT")}><i className="icon icon-chevron-right text-lg"></i></button>
        </div>

        {/* Date Selector */}
        <SelectJournee date={selectedDateJ} setter={handleDateChange} label={label} />
      </div>
       
    </div>
  );
};

export default CalendarHeader;
