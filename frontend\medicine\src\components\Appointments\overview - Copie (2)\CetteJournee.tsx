"use client";
import React from "react";
import Icon from '@mdi/react';
import { mdiAccountArrowDown ,mdiAccountArrowUp,mdiAccountDetails,mdiAccountEdit,mdiAccountConvert,mdiAccountFileText,mdiCalendarAccount,mdiAccountArrowLeft,mdiAccountFile
  ,mdiAccountAlert,mdiAlertPlus,mdiCommentPlus,mdiShareAll,mdiAccountCash,mdiCashMultiple,mdiArrowLeft,mdiBookmarkOutline,mdiAccountBoxPlusOutline,mdiCheck,mdiSeatLegroomExtra,

} from '@mdi/js';
//import { AlertsModal, AlertsButton } from '@/components/agenda/Appointments/AlertsModal';
import PatientRecord from './PatientRecord';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { TimeValue } from '@mantine/dates';
import {
  Patient,
  Appointment,
  AppointmentEvent,
  Event,
  ConsultationType,
  TitleOption,
  AgendaType,
  EventType
} from '@/types/typesCalendarPatient';
import { notifications } from '@mantine/notifications';
import { FaUserTie } from "react-icons/fa";
import { useForm } from '@mantine/form';
import { BsFillCalendar2DateFill } from "react-icons/bs";
import { MdOutlineUpdate } from "react-icons/md";
import { FaUserInjured } from "react-icons/fa6";
import {Badge,Tooltip,rem,Indicator ,Alert, Flex,List,Card,Avatar,Select,  Paper, Button, Modal, TextInput, NumberInput, Group,  Text, ActionIcon, Menu, Box, Switch
} from '@mantine/core';
import {  IconEdit, IconDental} from '@tabler/icons-react';
import { FloatingIndicator, Tabs } from '@mantine/core';
import { Loader } from '@mantine/core';

import { Container,  } from "@mantine/core";
import { IconDots,IconDotsVertical, IconArrowRight ,IconArrowsExchange} from "@tabler/icons-react";
import {ToolbarCalendarNav}  from "./ToolbarCalendarNav"
import MenuIcons from '@/components/agenda/icons/MenuIcons';
import { HiDotsVertical } from "react-icons/hi";
import { IconClock2 } from '@tabler/icons-react';
import { FaRegRectangleList } from "react-icons/fa6";
import { IoArrowForwardOutline } from "react-icons/io5";
import CalendarHeader from './CalendarHeader';
import { MdOutlineAutoDelete } from "react-icons/md";

import { FaUserDoctor } from "react-icons/fa6";
import { MdOutlineBedroomChild } from "react-icons/md";
import { BsCardChecklist } from "react-icons/bs";
import { IconCheck, IconX } from '@tabler/icons-react';

import { IconWallpaper } from '@tabler/icons-react';
import { IconArrowBackUpDouble } from '@tabler/icons-react';

import PatientList from "@/components/agenda/Appointments/PatientList";
import { BiSolidArrowFromLeft } from "react-icons/bi";

import AjouterUnRendezVous from './AjouterUnRendezVous';
import InfoModal from './InfoModal';
import PatientDetailsModal from './PatientDetailsModal';

import { ThemeIcon } from '@mantine/core';
import { ListPlus, ListTree,Users } from 'lucide-react';

import { MessagesSquare,} from 'lucide-react';
import { ClipboardType,MapPinHouse } from 'lucide-react';
import { FilePenLine } from 'lucide-react';
import {  ColorPicker } from '@mantine/core';
import { IconColorPicker } from '@tabler/icons-react';
import {
  CalendarClock,
  ChevronDown,
  ShieldCheck,
  Trash2,
  PhoneCall,
  AlarmClock,
} from "lucide-react";
import { IconInfoCircle, IconRefresh } from "@tabler/icons-react";
import SimpleBar from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";
import { Calendar as BigCalendar, Views, momentLocalizer } from 'react-big-calendar';
import type { EventInteractionArgs } from "react-big-calendar/lib/addons/dragAndDrop"; // Add this line
import withDragAndDrop from "react-big-calendar/lib/addons/dragAndDrop";
import appointmentService from '@/services/appointmentService';
import dentistryService from '@/services/dentistryService';
//import { DentistryAppointment } from '@/types/dentistry';
import {
  useState,
  useEffect,
  isValidElement,
  useCallback,

} from "react";
import { useDisclosure,  } from "@mantine/hooks";

import "react-big-calendar/lib/css/react-big-calendar.css";
import "./DayView.css";

import moment from "moment";
import "moment/locale/fr";
import TimeSlot from "@/components/agenda/Appointments/PatientsCalendar/TimeSlot";

import cx from "clsx";
import { useListState } from "@mantine/hooks";
type Gender = 'Homme ' | 'Femme' | 'Enfant';
import classes from "@/style/DndList.module.css";
import { IconStethoscope } from '@tabler/icons-react';
 const initialConsultationTypes: ConsultationType[] = [
 { value: '1er Consultation', label: '1er Consultation', duration: '15min' },
 { value: "Changement D'élastique'", label: "Changement D'élastique'", duration: '15min' },
 { value: 'Chirurgie/Paro', label: 'Chirurgie/Paro', duration: '30min' },
 { value: 'Collage', label: 'Collage', duration: '30min' },
 { value: 'Composite', label: 'Composite', duration: '15min' },
 { value: 'Consultation', label: 'Consultation', duration: '15min' },
 { value: 'Contention', label: 'Contention', duration: '30min' },
 { value: 'Contrôle', label: 'Contrôle', duration: '15min' },
 { value: 'Depose Sutures', label: 'Depose Sutures', duration: '15min' },
 { value: 'Detartrage', label: 'Detartrage', duration: '15min' },
 { value: 'Devis', label: 'Devis', duration: '15min' },
 { value: 'Echographie', label: 'Echographie', duration: '25min' },
 { value: 'Endodontie', label: 'Endodontie', duration: '50min' },
 { value: 'Formation', label: 'Formation', duration: '60min' },
 { value: 'Implantologie', label: 'Implantologie', duration: '55min' },
 { value: 'Obturation Canalaire', label: 'Obturation Canalaire', duration: '15min' },
 { value: 'Orthodontie', label: 'Orthodontie', duration: '25min' },
 { value: 'PA ES Chassis/Monta', label: 'PA ES Chassis/Monta', duration: '15min' },
 { value: 'PA Pose', label: 'PA Pose', duration: '15min' },
 { value: 'PC ESS Armature', label: 'PC ESS Armature', duration: '30min' },
 { value: 'PC Scellement', label: 'PC Scellement', duration: '15min' },
 { value: 'PC TP EMP', label: 'PC TP EMP', duration: '30min' },
 { value: 'Prophylaxie', label: 'Prophylaxie', duration: '15min' },
{ value: 'Urgent', label: 'Urgent', duration: '15min' },
{ value: 'comp', label: 'comp', duration: '90min' },
{ value: 'polissage', label: 'polissage', duration: '15min' },

  { value: 'ALICE', label: 'ALICE', duration: '15min' },
  { value: 'Analyse', label: 'Analyse', duration: '15min' },
  { value: 'Consultation pesée', label: 'Consultation pesée', duration: '15min' },

];
interface TimeSlotProps {
  value: Date;
  step: number;
  isRender: boolean;
  children: React.ReactNode;
  style?: React.CSSProperties; // Ajoutez cette ligne
  resource :number;
}
const localizer = momentLocalizer(moment);
moment.locale("fr");



const CetteJournee = () => {
const TimeSlotWrapper: React.FC = (props) => {
  // Forcer le cast car le calendrier ne fournit pas les types exacts
  const { value, children, resource } = props as TimeSlotProps;

  if (resource !== 1) {
    return <>{children}</>;
  }

  if (!isValidElement(children)) {
    return null;
  }

  return (
    <TimeSlot value={value} step={step} isRender={true}>
      {children}
    </TimeSlot>
  );
};

  const [isClient, setIsClient] = useState(false);
  const [loadingAppointments, setLoadingAppointments] = useState<boolean>(false);
  const [appointmentError, setAppointmentError] = useState<string | null>(null);
   const [isWaitingList, ] = useState(false);
  // Test API connectivity
  // const testApiConnection = async () => {
  //   try {
  //     console.log('🔗 Testing API connection...');
  //     const response = await fetch('/api/appointments/');
  //     console.log('API Response status:', response.status);
  //     const data = await response.json();
  //     console.log('API Response data:', data);

  //     if (response.ok) {
  //       alert('✅ API connection successful! Check console for details.');
  //     } else {
  //       alert(`❌ API error: ${response.status} - ${data.detail || 'Unknown error'}`);
  //     }
  //   } catch (error) {
  //     console.error('API test failed:', error);
  //     alert(`❌ API connection failed: ${error.message}`);
  //   }
  // };

  const fetchTodayAppointments = async () => {
    try {
      setLoadingAppointments(true);
      setAppointmentError(null);

      // Use the new dentistry service
      const dentistryAppointments = await dentistryService.getTodayAppointments();

      // Check if we got valid data
      if (!dentistryAppointments || !Array.isArray(dentistryAppointments)) {
        console.warn('Invalid dentistry appointments data:', dentistryAppointments);
        throw new Error('Invalid appointments data received from dentistry service');
      }

      // Convert dentistry appointments to legacy format for compatibility
      const processedAppointments = dentistryAppointments.map(appointment => {
        return dentistryService.convertToLegacyAppointment(appointment) as Appointment;
      });

      setAppointments(prevAppointments => {
        const existingIds = new Set(prevAppointments.map(a => a.id));
        const newAppointments = processedAppointments.filter(a => !existingIds.has(a.id));
        return [...prevAppointments, ...newAppointments];
      });

    } catch (error) {
      console.error('Error fetching today\'s appointments:', error);
      setAppointmentError('Failed to load appointments. Please try again.');

      // Fallback to old service if dentistry service fails
      try {
        const fallbackAppointments = await appointmentService.getTodayAppointments();
        const processedFallback = fallbackAppointments.map(appointment => {
          const patientId = appointment.patient?.id || '';
          const patientFirstName = appointment.patient?.first_name || '';
          const patientLastName = appointment.patient?.last_name || '';
          const resourceId = 'resourceId' in appointment && typeof appointment.resourceId === 'number'
            ? appointment.resourceId
            : 1;
          return {
                    ...appointment,
                    patientId,
                    resourceId,
                    name: patientFirstName,
                    prenom: patientLastName,
                    id: appointment.id,
                    title: `${patientFirstName} ${patientLastName}`,
                    first_name: patientFirstName,
                    last_name: patientLastName,
                    start: new Date(appointment.start || appointment.start_time || Date.now()),
                    end: new Date(appointment.end || appointment.end_time || Date.now() + 30 * 60 * 1000),
                    isActive: false,
                    consultationDuration: moment(appointment.end || appointment.end_time || Date.now() + 30 * 60 * 1000)
                      .diff(moment(appointment.start || appointment.start_time || Date.now()), 'minutes'),
                    type: appointment.type || appointment.appointment_type || 'visit',
                    eventType: appointment.type || appointment.appointment_type || 'visit',
                    gender: 'Homme',
                    telephone: appointment.telephone || '',
                    email: appointment.email || '',
                    address: appointment.address || '',
                    socialSecurity: appointment.socialSecurity || '',
                    cin: appointment.cin || '',
                    dateNaissance: appointment.dateNaissance || new Date(),
                    notes: appointment.notes || '',
                    doctor: appointment.doctor || '',
                    status: appointment.status || 'scheduled',
                    currentEventColor: getEventTypeColor((appointment.type || appointment.appointment_type || 'visit') as EventType),
                    birth_date: appointment.patient?.birth_date || '',
                    appointmentDate: moment(appointment.start || appointment.start_time || Date.now()).format('YYYY-MM-DD'),
                    appointmentTime: moment(appointment.start || appointment.start_time || Date.now()).format('HH:mm'),
                    appointmentEndTime: moment(appointment.end || appointment.end_time || Date.now() + 30 * 60 * 1000).format('HH:mm'),
                    age: appointment.patient?.age || 0,
                    phone_numbers: appointment.patient?.phone_numbers || '',
                    duration: appointment.duration || '30',
                    agenda: appointment.agenda || '',
                    comment: appointment.comment || '',
                    etatCivil: appointment.patient?.etatCivil || '',
                    etatAganda: appointment.patient?.etatAganda || '',
                    patientTitle: appointment.patient?.title || '',
                    date: moment(appointment.start || appointment.start_time || Date.now()).format('YYYY-MM-DD'),
                    docteur: appointment.doctor || '',
                    event_Title: appointment.event_Title || '',
                    sociale: appointment.socialSecurity || '',
                    typeConsultation: appointment.appointment_type || '',
                    commentairelistedattente: appointment.comment || '',
                    visitorCount: 0,
                  } as Appointment;
                });
          setAppointments(prevAppointments => {
          const existingIds = new Set(prevAppointments.map(a => a.id));
          const newAppointments = processedFallback.filter(a => !existingIds.has(a.id));
          return [...prevAppointments, ...newAppointments];
        });
      } catch (fallbackError) {
        console.error('Fallback service also failed:', fallbackError);
      }
    } finally {
      setLoadingAppointments(false);
    }
  };

  useEffect(() => {
    setIsClient(true);

    // Fetch appointments when component mounts
    fetchTodayAppointments();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps
  const currentDate = new Date();
  // French month names
  const months = [
    "janvier",
    "février",
    "mars",
    "avril",
    "mai",
    "juin",
    "juillet",
    "août",
    "septembre",
    "octobre",
    "novembre",
    "décembre",
  ];
  // French day names
  const days = [
    "dimanche",
    "lundi",
    "mardi",
    "mercredi",
    "jeudi",
    "vendredi",
    "samedi",
  ];
  const messages = {
    today: "Aujourd’hui",
    previous: "Précédent",
    next: "Suivant",
    month: "Mois",
    week: "Semaine",
    day: "Jour",
    agenda: "Agenda",
    noEventsInRange: "Aucun événement prévu",
  };
const DragAndDropCalendar = withDragAndDrop<Appointment>(BigCalendar);
  const dayOfWeek = days[currentDate.getDay()]; // Get day of the week (0-6)
  const day = currentDate.getDate(); // Get the day of the month (1-31)
  const monthIndex = currentDate.getMonth(); // Get the month (0-11)
  const year = currentDate.getFullYear(); // Get the full year (e.g., 2024)
  // Formulate the date string in French format
  const formattedDate = `${dayOfWeek} ${day} ${months[monthIndex]} ${year}`;
// Add this state near your other useState declarations
const [, setTotalVisitors] = useState(0);
     const [, setEvents] = useState<Event[]>([]);
     const [, setPatientEvents] = useState<Appointment[]>([]);
      const [, setShowAddModal] = useState(false);
      const [showViewModal, setShowViewModal] = useState(false);
      const [showEditModal, setShowEditModal] = useState(false);
      const [selectedEvent, setSelectedEvent] = useState<AppointmentEvent | null>(null);
      const [selectedSlot, setSelectedSlot] = useState<{start: Date, end: Date} | null>(null);
     // Patient form fields
      const [patientName, setPatientName] = useState('');
      const [patientlastName, setPatientlastName] = useState('');
      const [patientnom, ] = useState('');
      const [patientprenom, ] = useState('');
      const [email, setEmail] = useState('');
      const [address, setAddress] = useState('');
      const [patientsocialSecurity, setSocialSecurity] = useState('');
      const [patientduration, ] = useState('');
      const [patientagenda, ] = useState('');
      const [patientcomment, setPatientcomment ] = useState('');
      const [eventAganda, setEventAganda ] = useState('');
      const [patientdoctor, setPatientDocteur] = useState('');
      const [eventTitle, setEventTitle ] = useState('');
      const [patientnotes, setPatientNotes ] = useState('');
      const [patientsociale,  ] = useState('');
      const [patienttypeConsultation, setPatientTypeConsultation ] = useState('');
      const [patientcommentairelistedattente, setPatientCommentairelistedattente ] = useState('');
        const [eventDate, setEventDate] = useState('');
        const [eventTime, setEventTime] = useState('');
        const [eventConsultation, setEventConsultation] = useState('15 min'); // Default 30 minutes
        const [dureeDeLexamen, setDureeDeLexamen] = useState('15 min'); // Default 30 minutes
        const [eventDateDeNaissance, setEventDateDeNaissance] = useState('');
        const [eventAge, setEventAge] = useState<number | null>(null);
        const [genderOption, setGenderOption] = useState('Homme');
        const [eventCin, setEventCin] = useState('');
    const [patients, setPatients] = useState<Patient[]>([]);
    const [waitingList, setWaitingList] = useState<Patient[]>([]);
    const [appointments, setAppointments] = useState<Appointment[]>([]);
    const [activeVisits, setActiveVisits] = useState<Appointment[]>([]);
    const [searchValue, setSearchValue] = useState('');
    const [, setPatientModalOpen] = useState(false);
    const [infoModalOpen, setInfoModalOpen] = useState(false);
    const [currentPatient, setCurrentPatient] = useState<Patient | null>(null);
   const [eventResourceId, setEventResourceId] = useState<number>(1); // Default to Room A
     const [StartofworkOpened, { open: openedStartofwork, close: closeStartofwork }] = useDisclosure(false)
     const [ColorPickeropened, { open:openedColorPicker, close:closeColorPicker }] = useDisclosure(false);
     const [changeEndValue, setChangeEndValue] = useState('#FFFFFF');
     const [rescheduleModalOpen, setRescheduleModalOpen] = useState(false);
     const [appointmentToReschedule, setAppointmentToReschedule] = useState<Appointment | null>(null);
const EVENT_TYPE_COLORS = {
  'visit': "#34D1BF",
  'visitor-counter': "#F17105",
  'completed': "#3799CE",
  'diagnosis': "#F3124E",
  'default': "var(--mantine-color-dark-0)",
  'ALICE': "red",
  'Analyse': "blue",
  'Consultation': "#FF5252", // Added red color
  'Consultation pesée': "#FF9E80", // Added light orange
  'Contrôle': "orange",
  'Débaguage': "purple",
  'ELECRTO': "green",
  'Echographie': "green",
  'LIPO': "green",
  'Motif 1': "green",
  'Motif 2': "green",
  'PRESSO': "green",
  'Regime': "green",
  'SOIN DE VISAGE': "green",
  'microneedling': "green",
  'Urgence': "#FF9800", // Added orange
  'Avis': "#9C27B0", // Added purple
  'Autre': "#4CAF50", // Added green
};
 const [eventType, setEventType] = useState<EventType>('visit');
 const [viewPatient, setViewPatient] = useState<Patient | null>(null);
 const getEventTypeColor = (type: EventType | undefined) => {
   return type && EVENT_TYPE_COLORS[type] ? EVENT_TYPE_COLORS[type] : EVENT_TYPE_COLORS.default;
 };

const [titleOptions, setTitleOptions] = useState<TitleOption[]>([
  { value: "Mr", label: "Mr" },
  { value: "Mlle", label: "Mlle" },
  { value: "Mme", label: "Mme" },
  { value: "Dr.", label: "Dr." },
  { value: "Pr.", label: "Pr." },
  { value: "Garçon", label: "Garçon" },
  { value: "Fille", label: "Fille" },
  { value: "Bébé", label: "Bébé" },
]);
const [newTitle, setNewTitle] = useState<string>("");
const [agendaTypes, setAgendaTypes] = useState<AgendaType[]>([
  { value: "Cabinet", label: "Cabinet",  },
  { value: "Center", label: "Center",  },
  { value: "Kaders", label: "Kaders",  },
]);
const [newAgendaType, setNewAgendaType] = useState<string>("");
const [consultationTypes, setConsultationTypes] = useState<ConsultationType[]>(initialConsultationTypes);
const [newConsultationType, setNewConsultationType] = useState("");
const [newConsultationColor, setNewConsultationColor] = useState("#3799CE");
 const [Fichepatientpened, { open:openedFichepatient, close:closeFichepatient }] = useDisclosure(false);
useEffect(() => {
  if (!patientsocialSecurity || patientsocialSecurity.trim() === '') {
    setSocialSecurity('Aucune');
  }
}, [patientsocialSecurity]);
const [checkedAppelvideo, setCheckedAppelvideo] = useState<boolean>(false);
const [checkedRappelSms, setCheckedRappelSms] = useState<boolean>(false);
const [checkedRappelEmail, setCheckedRappelEmail] = useState<boolean>(false);
 const handleAppelvideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.checked;
    console.log("Setting Appel video to:", newValue);
    setCheckedAppelvideo(newValue);
  };
  const handleRappelSmsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.checked;
    console.log("Setting Rappel SMS to:", newValue);
    setCheckedRappelSms(newValue);
  };
  const handleRappelEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.checked;
    console.log("Setting Rappel Email to:", newValue);
    setCheckedRappelEmail(newValue);
  };
// To add an event ID to the Set
const [patientTitle,  ] = useState('');
const [type, setType] = useState("");
type EventType = "visit" | "visitor-counter" | "completed" | "diagnosis";
const colorMap: Record<EventType, string> = {
  visit: "#34D1BF",
  "visitor-counter": "#F17105",
  completed: "#3799CE",
  diagnosis: "#ED0423",
};
const color = colorMap[eventType];
  const [date, setDate] = useState(new Date());
  const handleNavigate = (action: "TODAY" | "PREV" | "NEXT" | "DATE", newDate?: Date) => {
    if (action === "TODAY") {
      setDate(new Date());
    } else if (action === "PREV") {
      setDate(moment(date).subtract(1, "day").toDate());
    } else if (action === "NEXT") {
      setDate(moment(date).add(1, "day").toDate());
    } else if (action === "DATE" && newDate) {
      setDate(newDate);
    }
  };
   const [minHour, setMinHour] = useState<number>(8); // Default to 8 AM
   const [minMinute, setMinMinute] = useState<number>(0); // Default to 0 minutes
   const [maxHour, setMaxHour] = useState<number>(18); // Default to 6 PM
   const [maxMinute, ] = useState<number>(0); // Default to 0 minutes
   const [step, setStep] = useState<number>(15); // Default
   const [minTime, setMinTime] = useState(() => {
     const date = new Date();
     date.setHours(8, 0, 0); // Default to 8:00 AM
     return date;
   });
   const [maxTime, setMaxTime] = useState(() => {
     const date = new Date();
     date.setHours(18, 0, 0); // Default to 6:00 PM
     return date;
   });
   const handleSave = () => {
     const newMinTime = moment()
       .startOf("day")
       .set({ hour: minHour, minute: minMinute })
       .local()
       .toDate();
     const newMaxTime = moment()
       .startOf("day")
       .set({ hour: maxHour, minute: maxMinute })
       .local()
       .toDate();
     setMinTime(newMinTime);
     setMaxTime(newMaxTime);
     console.log(newMaxTime);
   };
  
   const [rendezVousOpened, { open: openRendezVous, close: closeRendezVous }] =useDisclosure(false);
  const [ListDesPatientOpened, { open: openListDesPatient, close: closeListDesPatient }] = useDisclosure(false);
  const [AddwaitingListOpened, { open: AddwaitingList, close: closeAddwaitingList }] =useDisclosure(false);
  const [EditwaitingListOpened, { open: EditwaitingList, close: closeEditwaitingList }] =useDisclosure(false);
  const [ListRendezVousOpened, { open: openListRendezVous, close: closeListRendezVous }] = useDisclosure(false);
  const formatTime = (date: Date) => {
    if (!isClient) return ''; // Return empty string during SSR
    return date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };
  const icon = <IconInfoCircle />;
  // Create a counter that resets every day
  const [dailyCounter, setDailyCounter] = useState(0);
   const getCurrentDate = (): string => {
    const today = new Date();
    return today.toISOString().split("T")[0]; // Returns "YYYY-MM-DD"
  };
  useEffect(() => {
    const savedDate = localStorage.getItem("counterDate") ?? ""; // This will always be a string
    const currentDate = getCurrentDate(); // This will always return a string
    if (savedDate !== currentDate) {
      localStorage.setItem("counterDate", currentDate); // currentDate is guaranteed to be a string
      setDailyCounter(0); // Reset counter for the new day
    }
  }, []);
  const [activeIds, setActiveIds] = useState<number[]>([]); // Use an array to track multiple active IDs
  // First, add a helper function to check the visit status
const [opened, { open, close }] = useDisclosure(false)
type Resource = {
  id: number;
  title: string;
};
const resources: Resource[] = [
  { id: 1, title: 'Room A' },
  { id: 2, title: 'Room B' },
];
const [title, ] = useState('');
// const [description, ] = useState('');
// const [location, ] = useState('');
// const [attendees, ] = useState([]);
// const [notes, ] = useState('');
const [thirdModalOpened, { close: closeThirdModal }] = useDisclosure(false);

const [ fourthModalOpened, {  close: closeFourthModal },] = useDisclosure(false);
  const [, setActiveEventIds] = useState<Set<number>>(new Set());
  const [activeEvent_Ids, setActiveEvent_Ids] = useState<Set<number>>(new Set());
  const toggleEvent = (eventId: number) => {
    setActiveEventIds(prevIds => {
      const newIds = new Set(prevIds);
      if (newIds.has(eventId)) {
        newIds.delete(eventId);
      } else {
        newIds.add(eventId);
      }
      return newIds;
    });
  };
// To add an event ID to the Set
const addEventId = (eventId: number) => {
  setActiveEvent_Ids(prevIds => {
    const newIds = new Set(prevIds);
    newIds.add(eventId);
    return newIds;
  });
};
// To remove an event ID from the Set
const removeEventId = (eventId: number) => {
  setActiveEvent_Ids(prevIds => {
    const newIds = new Set(prevIds);
    newIds.delete(eventId);
    return newIds;
  });
};
     const patientForm = useForm({
       initialValues: {
         title: '',
         first_name: '',
         last_name: '',
         age: 0,
         phone_numbers: '',
         etatAganda: '',
         docteur: '',
         event_Title: "",
         gender:  "",
         socialSecurity: '',
         duration: '15 min',
         agenda: '',
         comment: '',
         address: '',
         type: eventType,
         resourceId:Number(eventResourceId),
      name:  '',
      prenom: '',
      email: '',
      typeConsultation:'',
      date :eventDate ?? Date,
      commentairelistedattente:'',
      etatCivil:  '', // Add this line
      patientTitle:'',
      birth_date: eventDateDeNaissance,
      consultationDuration: parseInt(eventConsultation),
    notes: patientnotes,
    cin: genderOption !== "Enfant" ? eventCin : undefined,
    appointmentDate: '',
    appointmentTime: '',
    appointmentEndTime: '',
  eventType:'',
  visitorCount: eventType === "visitor-counter" ? 1 : 0,
       },
     });

     const appointmentForm = useForm({
       initialValues: {
         patientId: '',
         notes: '',
         date: new Date(),
         duration: 15, // 30minutes
         type: "visit",
         resourceId:Number(eventResourceId),
         addToWaitingList: false,
         removeFromCalendar: false,
         rescheduleDateTime: '', // Add this field
       },
     });
   // Generate unique ID
   const generateId = (): string => {
    return (Math.floor(Math.random() * 10000) + 1).toString();
  };
 const handleDragEnd = (result: DropResult) => {
  if (!result.destination) return;
  const { source, destination } = result;
  if (destination.droppableId === source.droppableId && destination.index === source.index) {
    return;
  }

  // Handle reordering within activeVisits
  if (source.droppableId === 'dnd-list' && destination.droppableId === 'dnd-list') {
    const reorderedItems = Array.from(activeVisits);
    const [movedItem] = reorderedItems.splice(source.index, 1);
    reorderedItems.splice(destination.index, 0, movedItem);
    // Update the state with the new order
    setActiveVisits(reorderedItems);
    return;
  }

  // Handle waiting list to calendar transfer
  if (source.droppableId === 'waitingList' && destination.droppableId === 'calendar') {
    const patient = waitingList[source.index];

    // Create new appointment from patient data
    const newAppointment: Appointment = {
      id: generateId(),
      title: `${patient.first_name} ${patient.last_name}`,
      start: selectedSlot ? selectedSlot.start : new Date(),
      end: selectedSlot ?
        new Date(selectedSlot.start.getTime() + (patient.consultationDuration || 30) * 60000) :
        new Date(new Date().getTime() + 30 * 60000),
      patientId: patient.id,
      isActive: false,
      resourceId: Number(eventResourceId), // Default to Room A
      first_name: patient.first_name,
      last_name: patient.last_name,
      birth_date: patient.birth_date,
      age: patient.age,
      agenda: patient.agenda,
      etatCivil: patient.etatCivil,
      etatAganda: patient.etatAganda,
      cin: patient.cin,
      address: patient.address,
      phone_numbers: patient.phone_numbers,
      email: patient.email,
      docteur: patient.docteur || "",
      event_Title: patient.event_Title || "",
      gender:  patient.gender || "Homme",
      typeConsultation: patient.typeConsultation || "",
      notes: patient.notes || "",
      commentairelistedattente: patient.comment || "",
      sociale: patient.socialSecurity || "",
      appointmentDate: "",
      appointmentTime: "",
      appointmentEndTime: "",
      socialSecurity: patient.socialSecurity || "",
      duration: patient.consultationDuration?.toString() || "30",
      comment: patient.comment || "",
      patientTitle: patient.patientTitle || "",
      date: new Date().toISOString(),
      consultationDuration: patient.consultationDuration || 30,
      type: "visit",
      lunchTime: false,
      eventType:patient.eventType || "",
      currentEventColor: getEventTypeColor("visit"),
      visitorCount: eventType === "visitor-counter" ? 1 : 0,
      name: patient.name,
     prenom: patient.prenom,
    };

    // Add the new appointment to the appointments state (only once)
    setAppointments(prev => [...prev, newAppointment]);
    // Remove the patient from the waiting list (only once)
    setWaitingList(prev => prev.filter((_, index) => index !== source.index));
    // Show notification
    notifications.show({
      title: 'Appointment Created',
      message: `Appointment for ${patient.first_name} ${patient.last_name} has been scheduled`,
      color: 'blue',
      autoClose: 3000,
    });
    return;
  }
};
// Activate an appointment
const activateAppointment = (appointment: Appointment) => {
  const updatedAppointment = {
    ...appointment,
    isActive: !appointment.isActive
  };
   // Update appointments
   setAppointments(prev => prev.map(app =>
    app.id === appointment.id ? updatedAppointment : app
  ));
// Update active visits
if (updatedAppointment.isActive) {
  setActiveVisits(prev => [...prev, updatedAppointment]);
} else {
  setActiveVisits(prev => prev.filter(visit => visit.id !== appointment.id));
}
  const updatedAppointments = appointments.map(app =>
    app.id === appointment.id ? { ...app, isActive: true } : app
  );
  setAppointments(updatedAppointments);
  // Check if this appointment is already in activeVisits to prevent duplicates
  const isAlreadyActive = activeVisits.some(visit => visit.id === appointment.id);
  if (!isAlreadyActive) {
    setActiveVisits([...activeVisits, { ...appointment, isActive: true }]);
  }
  notifications.show({
    title: 'Appointment Activated',
    message: `${appointment.title}'s appointment is now active`,
    color: 'green',
    autoClose: 1000,
  });
};

// Remove an appointment
const removeAppointment = (appointment: Appointment) => {
  setAppointments(appointments.filter(app => app.id !== appointment.id));
  setActiveVisits(activeVisits.filter(app => app.id !== appointment.id));

  notifications.show({
    title: 'Appointment Removed',
    message: `Appointment for ${appointment.title} has been removed`,
    color: 'red',
  });
};
// Add this function with your other handlers
const handleLastVisit = (appointment: Appointment) => {
  const patient = patients.find(p => p.id === appointment.patientId);
  if (patient?.lastVisit) {
    setViewPatient(patient);
    setInfoModalOpen(true);
  } else {
    notifications.show({
      title: 'No Previous Visit',
      message: 'This patient has no recorded previous visits.',
      color: 'yellow',
      autoClose:1000
    });
  }
};
// Reschedule for next available time
const rescheduleAppointment = (appointment: Appointment) => {
  setAppointmentToReschedule(appointment);
  setRescheduleModalOpen(true);
  // For demo purposes, we'll just add a day
  const newStart = new Date(appointment.start);
  newStart.setDate(newStart.getDate() + 1);

  const newEnd = new Date(appointment.end);
  newEnd.setDate(newEnd.getDate() + 1);

  const updatedAppointment = {
    ...appointment,
    start: newStart,
    end: newEnd,
  };

  setAppointments(appointments.map(app =>
    app.id === appointment.id ? updatedAppointment : app
  ));

  notifications.show({
    title: 'Appointment Rescheduled',
    message: `${appointment.title}'s appointment has been rescheduled`,
    color: 'blue',
    autoClose:1000
  });
};
const [, setAlertsOpened] = useState(false);
 // Add these handler functions with your other functions
const handleAddAlert = (appointment: Appointment) => {
  notifications.show({
    title: 'Alert Added',
    message: `Alert set for ${appointment.title}'s appointment`,
    color: 'blue',
  });

};
const handleAddReminder = (appointment: Appointment) => {
  notifications.show({
    title: 'Reminder Added',
    message: `Reminder set for ${appointment.title}'s appointment`,
    color: 'blue',
  });
};
  const eventStyleGetter = (event: Appointment) => {
   const getEventStyle = (eventType: string) => {
     switch (eventType) {
       case 'visit':
         return '#34D1BF';
       case 'visitor-counter':
         return '#F17105';
       case 'completed':
         return '#3799CE';
       case 'diagnosis':
         return '#F3124E';
       default:
         return '#34D1BF';
     }
   };
     let baseStyle: React.CSSProperties = {
       // backgroundColor: event.color,
       // pointerEvents: event.disabled ? ("none" as const) : ("auto" as const),
       backgroundColor: getEventStyle(event.eventType),
       borderColor: getEventStyle(event.eventType),
       pointerEvents: event.disabled ? 'none' : 'auto',

     };
     if (event.lunchTime) {
       baseStyle = {
         ...baseStyle, // Conserver les styles existants
         // backgroundColor: "#06B1BD",
         color: "white",
         fontWeight: "bold",
         borderRadius: "4px",
         backgroundColor: event.color || "#3799CE",
         //opacity: 0.8,
         border: '0px',
         display: 'block',
         width: '100%',
         height: 'auto !important',
         transition: 'all 0.3s ease-in-out',
         cursor: 'pointer',
         overflow: 'hidden',
         padding: '8px'
       };

     }
     return { style: baseStyle }; // ✅ Type correctement assigné
   };
// Open edit patient form
const openEditPatient = (patient: Patient) => {
  setCurrentPatient(patient);
  patientForm.setValues({
    title: patient.title,
    first_name: patient.first_name,
    last_name: patient.last_name,
    age: patient.age,
    phone_numbers: patient.phone_numbers,
    socialSecurity: patient.socialSecurity,
    duration: patient.duration,
    agenda: patient.agenda,
    comment: patient.comment,
    address: patient.address,
  });
  setType(patient.type);
  setPatientName(patient.first_name);
  setPatientlastName(patient.last_name);
  setEventDateDeNaissance(patient.birth_date);
  setEventAge(patient.age);
  setEventEtatCivil(patient.etatCivil);
  setEventCin(patient.cin || '');
  setAddress(patient.address);
  setEventTelephone(patient.phone_numbers);
  setEmail(patient.email);
  setPatientDocteur(patient.docteur);
  setEventTitle(patient.event_Title);
  setGenderOption(patient.gender);
  setSocialSecurity(patient.socialSecurity);
  setPatientTypeConsultation(patient.typeConsultation);
  setEventAganda(patient.etatAganda);
  setPatientcomment(patient.comment);
  setPatientNotes(patient.notes);
  setEventResourceId(Number(eventResourceId));
  setEventDate(patient.appointmentDate);
  setEventTime(patient.appointmentTime);
  setDureeDeLexamen(patient.consultationDuration?.toString() || '15 min');
  EditwaitingList();
};

// Update patient
const handleUpdatePatient = (values: typeof patientForm.values) => {
    const startDateTime = moment(`${eventDate} ${eventTime}`).toDate();

  if (!currentPatient) return;
  const updatedPatient = {
    ...currentPatient,
    title: type,
    eventType: eventType,
    first_name: patientName,
    last_name: patientlastName,
    sociale: patientsocialSecurity, // Make sure this is updated
    socialSecurity: patientsocialSecurity,
    birth_date: eventDateDeNaissance,
    age: eventAge || 0,
    cin: eventCin,
    address: address,
    start: startDateTime,
    phone_numbers: eventTelephone,
    email: email,
    duration: dureeDeLexamen,
    end: moment(`${eventDate}T${eventTime}`).add(parseInt(dureeDeLexamen), 'minutes').toDate(),
    comment: patientcomment,
    notes: patientnotes,
    commentairelistedattente: patientcommentairelistedattente,
    resourceId: Number(eventResourceId),
    type: eventType,
    color: colorMap[eventType as EventType],
    appointmentDate: eventDate,
    appointmentTime: eventTime,
    appointmentEndTime: calculateEndTime(),
    consultationDuration: parseInt(dureeDeLexamen),
    etatCivil: eventEtatCivil,
    etatAganda:eventAganda,
    docteur: patientdoctor,
    event_Title:eventTitle,
    gender: genderOption,
    date: eventDate,
    typeConsultation: patienttypeConsultation,
    visitorCount: eventType === "visitor-counter" ? 1 : 0,
    name: values.name,
   prenom: values.prenom,
  };

  setPatients(patients.map(p => p.id === currentPatient.id ? updatedPatient : p));
  setWaitingList(waitingList.map(p => p.id === currentPatient.id ? updatedPatient : p));
  // Update appointment titles if this patient has appointments
  const fullName = `${values.title} `;
  setAppointments(appointments.map(app =>
    app.patientId === currentPatient.id ? { ...app, title: fullName } : app
  ));

  setActiveVisits(activeVisits.map(app =>
    app.patientId === currentPatient.id ? { ...app, title: fullName } : app
  ));
  notifications.show({
    title: 'Patient Updated',
    message: `${fullName}'s information has been updated`,
    color: 'blue',
    autoClose:1000
  });
  patientForm.reset();
  //resetForm();
  setCurrentPatient(null);
  setPatientModalOpen(false);
};

// Record a visit (simulate adding last visit data)

const handleEditClick = (appointment: Appointment) => {
  const patientData = patients.find(p => p.id === appointment.patientId);

  // Ensure patient data has the correct id that matches appointment.patientId
  const patientWithCorrectId = patientData ? {
    ...patientData,
    id: appointment.patientId // This ensures consistency
  } : undefined;

  const appointmentEvent: AppointmentEvent = {
    ...appointment,
    patient: patientWithCorrectId ? {
      ...patientWithCorrectId,
      title: appointment.title,
      first_name: appointment.first_name,
      last_name: appointment.last_name,
      resourceId: Number(eventResourceId), // Default to Room A
      birth_date: appointment.birth_date,
      age: appointment.age,
      agenda: appointment.agenda,
      etatCivil: appointment.etatCivil,
      etatAganda: appointment.etatAganda,
      cin: appointment.cin,
      address: appointment.address,
      phone_numbers: appointment.phone_numbers,
      email: appointment.email,
      docteur: appointment.docteur || "",
      event_Title: appointment.event_Title || "",
      gender:  appointment.gender || "Homme",
      typeConsultation: appointment.typeConsultation || "",
      notes: appointment.notes || "",
      commentairelistedattente: appointment.comment || "",
      sociale: appointment.socialSecurity || "",
      appointmentDate: "",
      appointmentTime: "",
      appointmentEndTime: "",
      socialSecurity: appointment.socialSecurity || "",
      duration: appointment.consultationDuration?.toString() || "30",
      comment: appointment.comment || "",
      patientTitle: appointment.patientTitle || "",
      date: new Date().toISOString(),
      consultationDuration: appointment.consultationDuration || 30,
      type: "visit",
      lunchTime: false,
      eventType:appointment.eventType || "",
      name: appointment.name,
     prenom: appointment.prenom,
    } : undefined
  };

  // Rest of the function remains the same
    setEventDate(moment(appointmentEvent.start).format('YYYY-MM-DD'));
  setEventTime(moment(appointmentEvent.start).format('HH:mm'));
  // Set other event details
  setGenderOption(appointmentEvent.gender || '');
  setEventEtatCivil(appointmentEvent.etatCivil || '');
  setEventCin(appointmentEvent.cin || '');
  setAddress(appointmentEvent.address || '');
  setEventTelephone(appointmentEvent.phone_numbers || '');
  setEmail(appointmentEvent.email || '');
  setPatientDocteur(appointmentEvent.docteur || '');
  setSocialSecurity(appointmentEvent.socialSecurity || 'Aucune');
  setPatientTypeConsultation(appointmentEvent.typeConsultation || '');
  setEventAganda(appointmentEvent.etatAganda || '');
  setEventType(appointmentEvent.type as EventType || 'visit');
  setPatientcomment(appointmentEvent.comment || '');
  setPatientNotes(appointmentEvent.notes || '');
  setEventTitle(appointmentEvent.event_Title|| '');
  setPatientName(appointmentEvent.first_name || '');
  setPatientlastName(appointmentEvent.last_name || '');
  setEventDateDeNaissance(appointmentEvent.birth_date || '');
  setEventAge(appointmentEvent.age || 0);
  setEventType(appointmentEvent.eventType as EventType || 'visit');
  setEventResourceId(Number(eventResourceId));
  setShowEditModal(true);
  setSelectedEvent(appointmentEvent);
};

const resetForm = () => {
  setPatientName('');
  setPatientlastName('');
  setType('');
  setEventType("visit");
  setPatientName('');
  setPatientlastName('');
  setEventEtatCivil('');
  setPatientDocteur('');
  setEventTitle('');
 setEventEtatCivil('');
  setEventAganda('');
  setPatientTypeConsultation('');
  setSearchValue('');
  setPatientCommentairelistedattente('');
  setPatientcomment('');
  setEventTelephone('');
  setEventSociale('');
  setSocialSecurity('');
  setPatientcomment('');
  setPatientNotes('');
  setEmail('');
  setAddress('');
   setEventConsultation('15');
  setEventDateDeNaissance('');
  setEventAge(null);
  setGenderOption('Homme');
  setEventCin('');
  setSelectedSlot(null);
  setSelectedEvent(null);
  setDureeDeLexamen('15 min');
    setEventDate(moment().format('YYYY-MM-DD')); // Set to current date
  setEventTime(moment().format('HH:mm')); // Set to current time
  setEventConsultation('15');
  setCheckedAppelvideo(false);
  setCheckedRappelSms(false);
  setCheckedRappelEmail(false);
    // Reset the form values for the switches
    if (appointmentForm) {
      appointmentForm.setValues({
        ...appointmentForm.values,
        addToWaitingList: false,
        removeFromCalendar: false
      });
    }
};
const handleRemoveLunchEvent = (eventId: string, resourceId: number | undefined) => {
  setPatientEvents(prevEvents => {
    const newEvents = prevEvents.filter(e =>
      !(e.id === eventId && e.resourceId === (resourceId || 0))
    );
    console.log('Removing event:', eventId, 'New events:', newEvents); // Debug log
    return newEvents;
  });

  // Also update appointments if needed
  setAppointments(prevAppointments =>
    prevAppointments.filter(app => app.id !== eventId)
  );

  notifications.show({
    title: 'Lunch Event Removed',
    message: 'The lunch event has been successfully removed',
    color: 'green',
  });
};
    const [lunchTimeDeleteOpened, { open: openedlunchTimeDelete, close: closelunchTimeDelete }] = useDisclosure(false);
    const [lunchColorPickeropened, { open:openedColorPickerlunch, close:closeColorPickerlunch }] = useDisclosure(false);
    const [changeEndValuelunch, setChangeEndValuelunch] = useState('#15AABF');
// Appointment Event Component
const EventComponent = ({ event }: { event: Appointment }) => {
  const appointment = event;
  //const eventId = Number(event.id);

  return (
    <>
          {appointment.lunchTime  ?
          <div  style={{ backgroundColor:`${changeEndValuelunch}`}} >{/*className="bg-[#15AABF]"*/}
           <Group className="flex  items-center w-full  pl-1 h-[26px]">
               <Group  className={isSidebarVisible ?  "w-[81%] ": "w-[91%]"}>
           <IconClock2 stroke={1.25} />
           <div className="font-bold " >{appointment.title}</div>
           </Group>
           <MdOutlineAutoDelete size={18} className="hover:fill-[#ED0423] " onClick={openedlunchTimeDelete}   />
          <Modal
              opened={lunchTimeDeleteOpened}
              onClose={closelunchTimeDelete}
              withCloseButton={false}
            >
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                Êtes-vous sûr de vouloir supprimer l&apos;heure du déjeuner ?
              </Alert>
              <Group justify="space-between" mt="md" mb="xs">
                <Button
                  variant="filled"
                  size="xs"
                  radius="lg"
                  color="red"

                  onClick={() => {
                    handleRemoveLunchEvent(appointment.id, appointment.resourceId);
                    closelunchTimeDelete(); // Close the modal after deletion
                  }}
                >
                  Oui
                </Button>
                <Button
                  variant="filled"
                  size="xs"
                  radius="lg"
                  color="lime.4"
                  onClick={closelunchTimeDelete}
                >
                  Non
                </Button>
              </Group>
            </Modal>
           </Group>
           </div>
          :
    <>
   {/* <div className="event-content" style={{ backgroundColor:event.color }} >  */}
   <div className="event-content" style={{ backgroundColor: event.currentEventColor || event.color || getEventTypeColor(event.eventType as EventType) }} >
   <Group className="flex justify-between items-center w-full pt-.5 pl-1 h-[26px]">
    <Menu  shadow="md" width={429} position="bottom-start" transitionProps={{ transition: 'rotate-right', duration: 150 }} withArrow arrowPosition="center">
    <Menu.Target >
      <div className="font-bold flex" onClick={(e) => e.stopPropagation()}>
        <span className=" ml-2">
      <FaRegRectangleList size={16}/>
      </span>
      <span className="mt-[1.2px] ml-2 ">

      <p className="capitalize">{appointment.last_name} {appointment.first_name}</p>
      </span>
      <Group gap="xs" className={isSidebarVisible ?  "ml-2 ": "ml-20"} >
        <span><IconClock2 size={16} /></span>
      <span className=" -ml-2">{appointment.consultationDuration} min </span>
      </Group>
      </div>
    </Menu.Target>
    <Menu.Dropdown onClick={(e) => e.stopPropagation()}
      style={{ backgroundColor: appointment.color }}
      className="left-[156px]"
      >
      <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Menu.Divider />
      <List size="sm" withPadding className="capitalize" type="ordered">
      <List.Item
      icon={
        <>
        <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
          <FaUserTie color={"#3799CE"} />
          <IoArrowForwardOutline size={16} color={appointment.color} />
          </Flex>
        </>
      }

    >
     {appointment.last_name} {appointment.first_name}
        </List.Item>

        <List.Item
      icon={
        <>
        <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
          <BsFillCalendar2DateFill color={"#3799CE"} />
          <IoArrowForwardOutline size={16} color={appointment.color} />
          </Flex>
        </>
      }
    >

      {moment(appointment.start).format("DD/MM/YYYY")}
        </List.Item>
    <List.Item
    icon={
    <>
    <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
      <MdOutlineUpdate color={"#3799CE"} />
      <IoArrowForwardOutline size={16} color={appointment.color} />
      </Flex>
    </>
    }
    >
      {moment(appointment.start).format("HH:mm")}- {moment(appointment.end).format("HH:mm")}
        </List.Item>

        <List.Item
      icon={
      <>
      <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
        <FaUserDoctor color={"#3799CE"} />
        <IoArrowForwardOutline size={16} color={appointment.color} />
        </Flex>
      </>
    }
      >
      {appointment.docteur}
        </List.Item>
        <List.Item
      icon={
        <>
        <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
          <BsCardChecklist  color={"#3799CE"} />
          <IoArrowForwardOutline size={16} color={appointment.color} />
          </Flex>
        </>
      }
    >
      {appointment.typeConsultation}
        </List.Item>
    </List>
    <Menu.Divider />

    </Card>
      </Menu.Dropdown>
    </Menu>
    <Menu shadow="md" width={200} position="bottom-end">
    <Menu.Target>
                  <span
                    className={`event.color hover:${appointment.color} rounded-full p-1 ml-auto`}
                    onClick={(e) => e.stopPropagation()}
                  >
                     <Group>
                     <Indicator
                      processing
                      inline
                      size={10}
                      offset={0}
                      position="bottom-end"
                      color="red"
                      withBorder
                      className={appointment.lunchTime ? "hidden" : ""}
                      disabled={!activeEvent_Ids.has(Number(event.id)) || activeIds.includes(Number(event.id))}
                      //  activeEventIds.has(Number(event.id))
                      mr={isSidebarVisible ?  40: 65}
                      
                    />
                    <HiDotsVertical size={20} />
                    </Group>
                  </span>
                </Menu.Target>
                <Menu.Dropdown onClick={(e) => e.stopPropagation()}>
                <Menu.Item leftSection={<MenuIcons.sltVisit size={16} />}>
                <Group className="flex justify-between items-center w-full pt-.5 pl-1">
                SLT
    {/* أكتر من غرفة */}
            <Button component="div" size="compact-xs" color="#15AABF">
              {activeVisits.filter(visit => visit.resourceId === appointment.resourceId).length}/30
            </Button>

                </Group>
            </Menu.Item>
                <Menu.Divider />
             <Menu.Item
            leftSection={<MenuIcons.activeVisit size={16} />}
            onClick={() => {
              if (appointment.isActive) {
                // If already active, remove from activeVisits
                setActiveVisits(activeVisits.filter(visit => visit.id !== appointment.id));
                // Update the appointment's isActive status
                setAppointments(appointments.map(app =>
                  app.id === appointment.id ? { ...app, isActive: false } : app
                ));
              } else {
                // If not active, activate it
                activateAppointment(appointment);
              }
            }}
>
  <Text c={appointment.isActive ? 'red' : 'green'}>
    {appointment.isActive ? 'Désactiver la visite' : 'Activer la visite'}
  </Text>
</Menu.Item>

    <Menu.Item leftSection={<MenuIcons.lastVisit size={16} />} onClick={() => handleLastVisit(appointment)}>
      Dernière visite
    </Menu.Item>
    <Menu.Divider />
      <Menu.Item
    leftSection={<MenuIcons.editAppointment size={16} />}
    onClick={() => {handleEditClick(appointment)}}
    >
    Modifier RDV
    </Menu.Item>
    <Menu.Item leftSection={<MenuIcons.nextAppointment size={16} />} onClick={() => rescheduleAppointment(appointment)} >
      Prochain RDV
    </Menu.Item>
    <Menu.Divider />
    <Menu.Item leftSection={<MenuIcons.patientFile size={16} />} onClick={() => openedFichepatient()}>
      Fiche patient
    </Menu.Item>
    <Menu.Item
    leftSection={<MenuIcons.patientDetails size={16} />}
    onClick={() => {
      setSelectedEvent(appointment);
      setShowViewModal(true);
    }}>
      Détails patient
    </Menu.Item>
     <Menu.Item
    leftSection={<MenuIcons.IconDental size={16} />}
    // onClick={() => {
    //   setSelectedEvent(appointment);
    //   setShowViewModal(true);
    // }}
    >
      Schéma dentaire
    </Menu.Item>
    <Menu.Divider />
    <Menu.Item leftSection={<MenuIcons.addAlert size={16} />}
    onClick={() => {
      handleAddAlert(appointment);
       setAlertsOpened(true)
    }
  }
    >
      Ajouter une alerte
    </Menu.Item>
    <Menu.Item leftSection={<MenuIcons.addReminder size={16} />} onClick={() => handleAddReminder(appointment)}>
      Ajouter un rappel
    </Menu.Item>
    <Menu.Divider />
    <Menu.Item
    leftSection={<MdOutlineAutoDelete  size={16} />}
    onClick={() => removeAppointment(appointment)}
    >
      Supprimer RDV
    </Menu.Item>
    <Menu.Divider />
    <Menu.Item  color="red" leftSection={<MenuIcons.cancel size={16} color="red" />}>
      Annuler
    </Menu.Item>
        </Menu.Dropdown>
    </Menu>
    </Group>
    </div>
    </>
    }

    </>
  );
};
const handleSubmit = (formValues: typeof appointmentForm.values) => {
  // if (!patientName || !eventDate || !eventTime || !eventConsultation) {
  if (!patientName ) {
    alert('Please fill in all required fields');
    return;
  }

   if (formValues.addToWaitingList) {
     // Create a new patient object for the waiting list
     const newWaitingListPatient: Patient = {
       id: selectedEvent?.id || generateId(),
       first_name: patientName || selectedEvent?.first_name || "",
       last_name: patientlastName || selectedEvent?.last_name || "",
       birth_date: eventDateDeNaissance || selectedEvent?.birth_date || "",
       age: eventAge || selectedEvent?.age || 0,
       gender: genderOption || selectedEvent?.gender || "",
       etatCivil: eventEtatCivil || selectedEvent?.etatCivil || "",
       etatAganda: eventAganda || selectedEvent?.etatAganda || "",
       cin: eventCin || selectedEvent?.cin || "",
       address: address || selectedEvent?.address || "",
       phone_numbers: eventTelephone || selectedEvent?.phone_numbers || "",
       email: email || selectedEvent?.email || "",
       docteur: patientdoctor || selectedEvent?.docteur || "",
       typeConsultation: patienttypeConsultation || selectedEvent?.typeConsultation || "",
       notes: patientnotes || selectedEvent?.notes || "",
       comment: patientcomment || selectedEvent?.comment || "",
       commentairelistedattente: patientcommentairelistedattente || selectedEvent?.commentairelistedattente || "",
       socialSecurity: patientsocialSecurity || selectedEvent?.socialSecurity || "",
       consultationDuration: parseInt(eventConsultation) || selectedEvent?.consultationDuration || 15,
       patientTitle: patientTitle || selectedEvent?.patientTitle || "",
       name: patientName || selectedEvent?.name || "",
       prenom: patientlastName || selectedEvent?.prenom || "",
       eventType: eventType || selectedEvent?.eventType || "visit",
       // Add the missing required properties
       title: type || selectedEvent?.title || "",
       appointmentDate: eventDate || selectedEvent?.appointmentDate || "",
       appointmentTime: eventTime || selectedEvent?.appointmentTime || "",
       appointmentEndTime: calculateEndTime() || selectedEvent?.appointmentEndTime || "",
       duration: dureeDeLexamen || selectedEvent?.duration || "15 min",
       agenda: patientagenda || selectedEvent?.agenda || "",
       date: eventDate || selectedEvent?.date || new Date().toISOString(),
       sociale: patientsociale || selectedEvent?.sociale || "",
       type: eventType || selectedEvent?.type || "visit",
       patientId: selectedEvent?.patientId || generateId(),
       event_Title: eventTitle || selectedEvent?.event_Title || "",
       resourceId: eventResourceId || selectedEvent?.resourceId || 0,
     };

     // Add to waiting list

     setWaitingList(prev => [...prev, newWaitingListPatient]);

     // If this is an existing event with an ID, remove it from the calendar
     if (selectedEvent && selectedEvent.id) {
       // Convert IDs to the same type for comparison
       setAppointments(prevAppointments =>
         prevAppointments.filter(appointment => appointment.id !== selectedEvent.id)
       );
     }

     // Show success notification
     notifications.show({
       title: 'Patient added to waiting list',
       message: 'The appointment has been removed from the calendar',
       color: 'teal',
       autoClose: 3000,
     });
     // Close the modal
     closeRendezVous();
     resetForm();
   } else {
     // Create start and end date objects
  const startDateTime = moment(`${eventDate} ${eventTime}`).toDate();
   const endDateTime = moment(`${eventDate} ${calculateEndTime()}`).toDate();
   // Create patient object
  const newPatient: Patient = {
     id: generateId(),
     title: title,
     last_name: patientlastName,
     first_name: patientName,
     name: patientnom,
     prenom: patientprenom,
     socialSecurity: patientsocialSecurity,
     duration: patientduration,
     agenda: patientagenda,
     comment: patientcomment,
     notes: patientnotes,
     birth_date: eventDateDeNaissance,
     age: eventAge || 0,
     cin: genderOption !== "Enfant" ? eventCin : undefined,
     phone_numbers: eventTelephone,
     email: email,
     address: address,
     appointmentDate: eventDate,
     appointmentTime: eventTime,
     appointmentEndTime: calculateEndTime(),
     consultationDuration: parseInt(dureeDeLexamen),
     patientTitle:patientTitle,
     etatCivil:eventEtatCivil,
     etatAganda:eventAganda,
     docteur: patientdoctor,
     event_Title:eventTitle,
     gender:genderOption,
     date :eventDate ?? Date,
     sociale: patientsociale,
     typeConsultation: patienttypeConsultation,
     commentairelistedattente: patientcommentairelistedattente,
     resourceId: Number(eventResourceId),
     type:eventType,
     eventType:eventType,
     patientId:generateId(),

   };
   // Create appointment event
   const newAppointment: Appointment = {
     id: generateId(),
     title: newPatient.title,
     start: startDateTime,
     end: endDateTime,
     patientId: newPatient.id,
     isActive: false,
     etatCivil: newPatient.etatCivil,
     etatAganda: newPatient.etatAganda,
     patientTitle: newPatient.title,
     birth_date: newPatient.birth_date,
     age: newPatient.age,
     email: newPatient.email,
     date: eventDate,
     docteur: newPatient.docteur,
     event_Title: newPatient.event_Title,
     gender:newPatient.gender,
     sociale: newPatient.socialSecurity,
     typeConsultation: newPatient.typeConsultation,
     agenda: newPatient.agenda,
     commentairelistedattente: newPatient.comment,
     resourceId: Number(eventResourceId),
      last_name: patientlastName,
     first_name: patientName,
     name: patientnom,
     prenom: patientprenom,
     socialSecurity: patientsocialSecurity,
     duration: patientduration,
     comment: patientcomment,
     notes: patientnotes,
     cin: genderOption !== "Enfant" ? eventCin : undefined,
     phone_numbers: eventTelephone,
     address: address,
     appointmentDate: eventDate,
     appointmentTime: eventTime,
     appointmentEndTime: calculateEndTime(),
     consultationDuration: parseInt(dureeDeLexamen),
     type: eventType,
     color: colorMap[eventType],
     eventType:eventType,
     currentEventColor: getEventTypeColor(eventType),
     visitorCount: eventType === "visitor-counter" ? 1 : 0, // Add visitor
   };
   setPatients(prev => [...prev, newPatient]);
   setAppointments(prev => [...prev, newAppointment]); // Add this line to update appointments
   // Rest of your code...
   setShowAddModal(false);
   resetForm();
   // patientForm.reset();
   notifications.show({
     title: 'Appointment Created',
     message: `Appointment for ${patientName} ${patientlastName} has been scheduled`,
     color: 'blue',
     autoClose: 1000,
   });
   if (eventType === "visitor-counter") {
     // setActiveVisits(prev => [...prev, newAppointment]);
     setTotalVisitors(prev => prev + 1);
   }
   setShowAddModal(false);
   resetForm();
   }

  // Update your visitor counter logic when adding new appointments
console.log("setTotalVisitors:",setTotalVisitors)
  // Update both patients and appointments states
};
// Handle edit submission
// Update the handleEditSubmit function
const handleEditSubmit = (e: React.FormEvent) => {
  e.preventDefault();
    // Get the form values
    const formValues = appointmentForm.values;
    if (selectedEvent && formValues.addToWaitingList) {
      // Create a new waiting list patient from the selected event
      const newWaitingListPatient: Patient = {
        ...selectedEvent,
        id: selectedEvent.patientId || selectedEvent.id, // Use patientId if available, otherwise use id
        // Make sure all required Patient fields are included
        agenda: selectedEvent.agenda || "",
        event_Title: selectedEvent.event_Title || "",
        gender: selectedEvent.gender || "Homme",
        resourceId: selectedEvent.resourceId || 1,
        // Add any other required fields
      };
      // Check if patient already exists in waiting list
      const patientExists = waitingList.some(p => p.id === newWaitingListPatient.id);
      if (!patientExists) {
        setWaitingList(prev => [...prev, newWaitingListPatient]);

        notifications.show({
          title: 'Added to Waiting List',
          message: `${selectedEvent.first_name} ${selectedEvent.last_name} has been added to the waiting list`,
          color: 'blue',
          autoClose: 1000,
        });
      } else {
        notifications.show({
          title: 'Already in Waiting List',
          message: `${selectedEvent.first_name} ${selectedEvent.last_name} is already in the waiting list`,
          color: 'yellow',
          autoClose: 1000,
        });
      }
      // Remove from calendar if needed
      if (formValues.removeFromCalendar) {
        setAppointments(prev => prev.filter(appointment => appointment.id !== selectedEvent.id));
      }

      // Show notification
      notifications.show({
        title: 'Patient added to waiting list',
        message: formValues.removeFromCalendar ?
          'The appointment has been removed from the calendar' :
          'The appointment remains on the calendar',
        color: 'teal',
        autoClose: 3000,
      });

      // Close modal and reset form
      setShowEditModal(false);
      resetForm();
      return;
    }
  if (selectedEvent) {
     // Calculate end time based on start time and consultation duration
     const startDateTime = `${eventDate}T${eventTime}`;
     const startMoment = moment(startDateTime);
     const endMoment = moment(startDateTime).add(
       parseInt(dureeDeLexamen) || 15,
       'minutes'
     );
    const updatedEvent: Appointment = {
      ...selectedEvent,
      id: selectedEvent.id,
      patientId: selectedEvent.patientId, // Use correct patientId
      isActive: selectedEvent.isActive || false,
      title: `${patientName} ${patientlastName}`,
      eventType: eventType,
      first_name: patientName,
      last_name: patientlastName,
      start: startMoment.toDate(),
      end: endMoment.toDate(),
      resourceId: Number(eventResourceId),
      sociale: patientsocialSecurity, // Make sure this is updated
      socialSecurity: patientsocialSecurity,
      birth_date: eventDateDeNaissance,
      age: eventAge || 0,
      cin: eventCin,
      address: address,
      phone_numbers: eventTelephone,
      email: email,
      duration: dureeDeLexamen,
      comment: patientcomment,
      notes: patientnotes,
      commentairelistedattente: patientcommentairelistedattente,
      type: eventType,
      color: colorMap[eventType as EventType],
      currentEventColor: getEventTypeColor(eventType),
      appointmentDate: eventDate,
      appointmentTime: eventTime,
      appointmentEndTime: calculateEndTime(),
      consultationDuration: parseInt(dureeDeLexamen),
      etatCivil: eventEtatCivil,
      etatAganda:eventAganda,
      docteur: patientdoctor,
      event_Title:eventTitle,
      gender:genderOption,
      patientTitle: selectedEvent.patientTitle,
      date: eventDate,
      typeConsultation: patienttypeConsultation,
      visitorCount: eventType === "visitor-counter" ? 1 : 0,

    };
    if (!selectedEvent) {
      notifications.show({
        title: 'Error',
        message: 'No event selected for editing',
        color: 'red',
        autoClose:1000
      });
      return;
    }

     setAppointments(appointments.map(event =>
      event.id === selectedEvent.id ? updatedEvent : event
    ));
     // Close the modal and reset form
     setShowEditModal(false);
     resetForm();
    if (updatedEvent.isActive) {
      setActiveVisits(prev => prev.map(visit =>
        visit.id === selectedEvent.id ? {
          ...updatedEvent,
          sociale: patientsocialSecurity, // Ensure sociale is updated in active visits
        } : visit
      ));
    }
    setShowEditModal(false);
    // resetForm();
    notifications.show({
      title: 'Appointment Updated',
      message: `Appointment for ${patientName} ${patientlastName} has been updated`,
      color: 'blue',
      autoClose:1000
    });
  }
};

 // Calculate end time based on start time and duration
    const calculateEndTime = () => {
      if (eventTime) {
        return moment(eventTime, "HH:mm")
          .add(parseInt(eventConsultation), "minutes")
          .format("HH:mm");
      }
      return "";
    };

    // Calculate age based on date of birth
    const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const birthDate = e.target.value;
      setEventDateDeNaissance(birthDate);

      if (birthDate) {
        const today = moment();
        const birthMoment = moment(birthDate);
        const age = today.diff(birthMoment, 'years');
        setEventAge(age);
      } else {
        setEventAge(null);
      }
    };
    const handleOptionChange = (value: string) => {
      // Type assertion to ensure value is of type Gender
      const genderValue = value as Gender;
      setGenderOption(genderValue);

      // Reset CIN if Child is selected
      if (genderValue === "Enfant") {
        setEventCin("");
      }
    };
// *********** AND Drag and Drop ListeDattente /////////////////////////////////////



 const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
 const [, setWaitingListEvents] = useState<Event[]>([]);
     // Toggle sidebar visibility
    const toggleSidebar = () => {
      setIsSidebarVisible(!isSidebarVisible);
    };
   // Handle creating an appointment from calendar click
//    const handleSlotSelect = (slotInfo: {start: Date, end: Date}) => {
//      setSelectedSlot(slotInfo);
//         // Pre-fill date and time from the selected slot
//          const slotDate = moment(slotInfo.start).format('YYYY-MM-DD');
//          const slotTime = moment(slotInfo.start).format('HH:mm');
//          setEventDate(slotDate);
//          setEventTime(slotTime);
//          setShowAddModal(true);
//          openRendezVous();
//      const { start, end } = slotInfo;
//        // Create a new appointment object with the selected time slot
//    const newAppointment = {
//      title: title,
//      description: description,
//      location: location,
//      startDate: start,
//      endDate: end,
//      attendees: attendees,
//      notes: notes
//    };
//  // 👈 à implémenter selon votre logique

//    resetForm();
//    };
   // Handle creating an appointment from calendar click
   const handleSlotSelect = (slotInfo: {start: Date, end: Date}) => {
    setSelectedSlot(slotInfo);
    // Pre-fill date and time from the selected slot
    const slotDate = moment(slotInfo.start).format('YYYY-MM-DD');
    const slotTime = moment(slotInfo.start).format('HH:mm');
    setEventDate(slotDate);
    setEventTime(slotTime);
    setShowAddModal(true);
    openRendezVous();
    
    // Remove or comment out the unused newAppointment declaration
    /* const newAppointment = {
      title: title,
      description: description,
      location: location,
      startDate: start,
      endDate: end,
      attendees: attendees,
      notes: notes
    }; */

    resetForm();
  };
   const onEventDrop = useCallback(
    (args: EventInteractionArgs<Appointment>) => {
      const { event, start, end, resourceId } = args;
       // Create the updated event with the correct resourceId
       const updatedEvent = {
        ...event,
        start: new Date(start),
        end: new Date(end),
        // Ensure resourceId is preserved and converted to number
        resourceId: resourceId ? Number(resourceId) : event.resourceId
      };
      console.log("Dropped event with resourceId:", updatedEvent.resourceId);
      const nextEvents = appointments.map((existingEvent) =>
        existingEvent.id === event.id ? updatedEvent : existingEvent
      );

      setAppointments(nextEvents);
    },
    [appointments],
  );
  const [eventTelephone, setEventTelephone] = useState<string>("");
  const [, setEventSociale] = useState<string>("");
  const [eventEtatCivil, setEventEtatCivil] = useState("");

  // const handleAddPatient =(formValues: typeof appointmentForm.values) => {
  //   if (!patientName ) {
  //     alert('Please fill in all required fields');
  //     return;
  //   }
  const handleAddPatient = (formValues: typeof appointmentForm.values) => {
  // Generate a unique ID for the patient
  const patientId = generateId();

  // Validate required fields
  if (!patientName || !patientlastName) {
    alert('Veuillez renseigner le nom et prénom du patient');
    return;
  }

    // Create start and end date objects
    const startDateTime = moment(`${eventDate} ${eventTime}`).toDate();
    const endDateTime = moment(`${eventDate} ${calculateEndTime()}`).toDate();
    const newPatient: Patient = {
      id: patientId,
      title: title,
      last_name: patientlastName,
      first_name: patientName,
      name: patientnom,
      prenom: patientprenom,
      start: startDateTime,
      end: endDateTime,
      socialSecurity: patientsocialSecurity,
      duration: patientduration,
      agenda: patientagenda,
      comment: patientcomment,
      notes: patientnotes,
      birth_date: eventDateDeNaissance,
      age: eventAge || 0,
      cin: genderOption !== "Enfant" ? eventCin : undefined,
      phone_numbers: eventTelephone,
      email: email,
      address: address,
      appointmentDate: eventDate,
      appointmentTime: eventTime,
      appointmentEndTime: calculateEndTime(),
      consultationDuration: parseInt(dureeDeLexamen),
      patientTitle:patientTitle,
      etatCivil:eventEtatCivil,
      etatAganda:eventAganda,
      docteur: patientdoctor,
      event_Title:eventTitle,
      gender:genderOption,
      date :eventDate ?? Date,
      sociale: patientsociale,
      typeConsultation: patienttypeConsultation,
      commentairelistedattente: patientcommentairelistedattente,
      resourceId: eventResourceId,
      type:eventType,
      eventType:eventType,
      patientId: patientId,

    };
    notifications.show({
      title: 'Patient Added',
      message: `${newPatient.first_name} ${newPatient.last_name} has been added to the waiting list`,
      color: 'green',
      autoClose:1000
    });
    setWaitingList([...waitingList, newPatient]);
    setPatients([...patients, newPatient]);
    // patientForm.reset();
    // setPatientModalOpen(false);
    resetForm();
    // setShowAddModal(false);
    closeAddwaitingList()
  };
   // -----------------------LunchEvent -----------------------------
  // const [lunchEvents, setLunchEvents] = useState<AppointmentEvent[]>([]);
    const [dateSelected, ] = useState<Date>(new Date());
    const [, setShowTimeSelector] = useState<boolean>(false);
    const [selectedHour, setSelectedHour] = useState<number | null>(null);
    const [selectedMinute, setSelectedMinute] = useState<number | null>(null);
    const [duration, setDuration] = useState<number>(60); // Durée par défaut: 60 minutes
      // Générer les options d'heures (11h à 14h)
    const hours = [11, 12, 13, 14, 15, 16];
      // Générer les options de minutes (0, 15, 30, 45)
    const minutes = [0, 15, 30, 45];
     // Options de durée (15min à 120min par intervalles de 15min)
  const durations = [15, 30, 45, 60, 75, 90, 105, 120];

  const addLunchEvent = () => {
    if (selectedHour === null || selectedMinute === null) return;

    const start = new Date(dateSelected);
    start.setHours(selectedHour, selectedMinute, 0);
    const end = new Date(start);
    end.setMinutes(end.getMinutes() + duration);

    const durationText = duration >= 60
      ? `${Math.floor(duration/60)}h${duration % 60 === 0 ? '' : duration % 60}`
      : `${duration}min`;

    // Create lunch event as Appointment type
    const newLunchEvent: Appointment = {
      id: generateId(),
      title: `Déjeuner à ${selectedHour}h${selectedMinute === 0 ? '00' : selectedMinute} (${durationText})`,
      start: start,
      end: end,
      patientId: 'lunch', // Required field for Appointment type
      isActive: false,    // Required field for Appointment type
      lunchTime: true,
      type: "visit",
      resourceId: eventResourceId,
      // Required fields with default values
      first_name: "",
      last_name: "",
      name: "",
      prenom: "",
      agenda: "",
      birth_date: "",
      age: 0,
      etatCivil: "",
      cin: "",
      address: "",
      phone_numbers: "",
      email: "",
      docteur: "",
      event_Title: "",
      gender: "",
      typeConsultation: "",
      notes: "",
      commentairelistedattente: "",
      sociale: "",
      appointmentDate: "",
      appointmentTime: "",
      appointmentEndTime: "",
      etatAganda: "",
      socialSecurity: "",
      duration: "",
      comment: "",
      patientTitle: "",
      date: start.toISOString(),
      consultationDuration: duration,
      eventType:"visit",
      visitorCount: eventType === "visitor-counter" ? 1 : 0,
    };

    // Update appointments state
    setAppointments(prev => [...prev, newLunchEvent]);

    // Reset form
    setShowTimeSelector(false);
    setSelectedHour(null);
    setSelectedMinute(null);
    setDuration(60);
    setEventResourceId(1);

    notifications.show({
      title: 'Lunch Break Added',
      message: 'Lunch break has been scheduled',
      color: 'green',
    });
  };

// Update your function that handles adding waiting list items to the calendar
const handleAddToCalendar = (patient: Patient) => {
  // Create a new appointment from the patient
  const newAppointment: Appointment = {
    id: patient.id || generateId(),
    title: `${patient.first_name} ${patient.last_name}`,
    start: new Date(`${patient.appointmentDate}T${patient.appointmentTime}`),
    end: new Date(`${patient.appointmentDate}T${patient.appointmentEndTime}`),
    patientId: patient.id || generateId(),
    isActive: false,
    resourceId: patient.resourceId || 1, // Default to 1 if not set
    etatCivil: patient.etatCivil,
    etatAganda: patient.etatAganda,
    patientTitle: patient.title,
    birth_date: patient.birth_date,
    age: patient.age,
    email: patient.email,
    date: eventDate,
    docteur: patient.docteur,
    event_Title: patient.event_Title,
    gender:patient.gender,
    sociale: patient.socialSecurity,
    typeConsultation: patient.typeConsultation,
    agenda: patient.agenda,
    commentairelistedattente: patient.comment,
    last_name: patientlastName,
    first_name: patientName,
    name: patientnom,
    prenom: patientprenom,
    socialSecurity: patientsocialSecurity,
    duration: patientduration,
    comment: patientcomment,
    notes: patientnotes,
    cin: genderOption !== "Enfant" ? eventCin : undefined,
    phone_numbers: eventTelephone,
    address: address,
    appointmentDate: eventDate,
    appointmentTime: eventTime,
    appointmentEndTime: calculateEndTime(),
    consultationDuration: parseInt(dureeDeLexamen),
    type: eventType,
    color: colorMap[eventType],
    eventType:eventType,
    currentEventColor: getEventTypeColor(eventType),
    visitorCount: eventType === "visitor-counter" ? 1 : 0, // Add visitor
  };

  // Add to appointments
  setAppointments(prev => [...prev, newAppointment]);

  // Optionally remove from waiting list
  setWaitingList(prev => prev.filter(p => p.id !== patient.id));

  // Show notification
  notifications.show({
    title: 'Added to Calendar',
    message: `${patient.first_name} ${patient.last_name} has been added to the calendar`,
    color: 'green',
    autoClose: 3000,
  });
};
// Create an adapter function that converts Event to Patient
const handleAddToCalendarAdapter = (event: Event) => {
  // Convert Event to Patient or create a new Patient from Event data
  const patientFromEvent: Patient = {
    id: (event.id || generateId()).toString(),
    first_name: event.title?.split(' ')[0] || '',
    last_name: event.title?.split(' ')[1] || '',
    appointmentDate: moment(event.start).format('YYYY-MM-DD'),
    appointmentTime: moment(event.start).format('HH:mm'),
    appointmentEndTime: moment(event.end).format('HH:mm'),
    consultationDuration: moment(event.end).diff(moment(event.start), 'minutes'),
     resourceId: event.resourceId  || 1, // Cast to any to access resourceId if available
    title: '',
    name: '',
    prenom: '',
    agenda: '',
    birth_date: '',
    age: 0,
    docteur: '',
    event_Title: '',
    phone_numbers: '',
    etatAganda: '',
    gender:  "",
    socialSecurity: '',
    duration: '15 min',
    comment: '',
    address: '',
    type: eventType,
 email: '',
 typeConsultation:'',
 date :eventDate ?? Date,
 commentairelistedattente:'',
 etatCivil:  '', // Add this line
 patientTitle:'',
notes: patientnotes,
cin: genderOption !== "Enfant" ? eventCin : undefined,
sociale: "",
eventType:"visit",
  };

  // Call your original handler with the converted patient
  handleAddToCalendar(patientFromEvent);
};
// const [state, handlers] = useListState<Event>(events);
const [eventIdCounter,] = useState(1);


    // Generate draggable items
const [VisitesActivesDeleteOpened, { open: openedVisitesActivesDelete, close: closeVisitesActivesDelete }] = useDisclosure(false);
   // const [, setOpenedModalId] = useState<number | null>(null);

  //const closeModal = () => setOpenedModalId(null);
  const deleteVisits = (id: number | string, fromWaitingList = false) => {
    // Convert id to number if it's a string
    const numericId = typeof id === 'string' ? parseInt(id, 10) : id;

    if (isNaN(numericId)) {
      console.error("Invalid event ID:", id);
      notifications.show({
        title: 'Error',
        message: 'Could not delete event: Invalid ID',
        color: 'red',
      });
      return;
    }

    // For debugging
    // console.log("Deleting event with ID:", numericId);
    // console.log("Current events:", events);
    // console.log("Current activeVisits:", activeVisits);

    if (fromWaitingList) {
      setWaitingListEvents((prevEvents) => {
        return prevEvents.filter((event) => Number(event.id) !== numericId);
      });
    } else {
      setEvents((prevEvents) => {
        return prevEvents.filter((event) => Number(event.id) !== numericId);
      });

      // Also update appointments
      setAppointments((prevAppointments) => {
        return prevAppointments.filter((appointment) => Number(appointment.id) !== numericId);
      });
    }

    // Also remove from activeVisits if present
    setActiveVisits((prevVisits) => {
      return prevVisits.filter((event) => Number(event.id) !== numericId);
    });

    // Close the modal after deletion
    closeVisitesActivesDelete();
  };
    const [, handlers] = useListState(appointments);

    // tab content Visites Actives
    const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null);
    const [value, setValue] = useState<string | null>('1');
    const [controlsRefs, setControlsRefs] = useState<Record<string, HTMLButtonElement | null>>({});
    const setControlRef = (val: string) => (node: HTMLButtonElement) => {
      controlsRefs[val] = node;
      setControlsRefs(controlsRefs);
    };
  useEffect(() => {
    if (genderOption === "Enfant") {
      setEventCin(""); // Clear CIN when "Enfant" is selected
    }
  }, [genderOption]);

 // const isMobile = useMediaQuery("(max-width: 50em)");
  // const firstOpenModal = () => {
  //   openRendezVous();
  // };
  useEffect(() => {
    // Retrieve the saved date from localStorage, defaulting to an empty string
    const savedDate = localStorage.getItem("counterDate") ?? ""; // This will always be a string
    const currentDate = getCurrentDate(); // This will always return a string
    // Compare and update the localStorage if the date has changed
    if (savedDate !== currentDate) {
      localStorage.setItem("counterDate", currentDate); // currentDate is guaranteed to be a string
      setDailyCounter(0); // Reset counter for the new day
    }
  }, []);
    return (
      <>
    <div className="header-nav-base mb-2 p-1">
           <div className="flex justify-between">
             <div className="flex gap-4">
                <div className="flex justify-start">
                                   {/* liste d'attente */}
                <ThemeIcon className="cursor-pointer">
                  <ListTree  size={30}  className="bg-[#E6E9EC] text-[#3799CE] " onClick={(event) => {
                        event.preventDefault();
                        toggleSidebar(); // Toggle sidebar visibility
                      }}/> 
                </ThemeIcon>
                <Avatar size={26}  radius={30} variant="filled" color="cyan" className="border-2 rounded-full border-[#fff] mr-[2px] ">
                  <Text fz="sm" fw={500} c={"white"}>
                  {waitingList?.length || 0}
                  </Text>
                  </Avatar>
                <ThemeIcon className="cursor-pointer">
                <ListPlus size={30} className="bg-[#E6E9EC] text-[#3799CE] " onClick={() => {
                    AddwaitingList();
                    resetForm()
                  }}
                                  />
                </ThemeIcon>
			
                <ThemeIcon className="cursor-pointer" style={{background:"var(--header-nav-base)"}}>
                <Users size={20} className="bg-[#E6E9EC] text-[#3799CE] " onClick={() => {
                    AddwaitingList();
                    resetForm()
                  }}
                                  />
                </ThemeIcon>
                 
                                  </div>
               <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4 capitalize">
                 <CalendarClock className="mr-1 h-3.5 w-3.5" />
                 {formattedDate}
               </h3>
               <span className="-mx-6 p-1">|</span>
               <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
                 {" "}
                 {/* Aujour&apos;hui   */}
               </h3>
               <Modal
            opened={opened}
            onClose={close}
            size="auto"
             title={`Sélectionnez l'heure et la durée du déjeuner :`}
          >
           <div className="time-selector p-4 border rounded mb-4">
           <div className="grid gap-2">
                <div className="flex gap-2 mb-2">
            {/* Sélection de l'heure */}
                  <Select
              id="lunchHour"
              label="Heure:"
              placeholder="Heure:"
             onChange={(value) => setSelectedHour(Number(value))}
              value={selectedHour === null ? "" : selectedHour.toString()}
              data={hours.map(hour => ({ value: hour.toString(), label: `${hour}h` }))}
            />
            {/* Sélection des minutes */}
        <Select
          id="lunchMinute"
          label="Minutes:"
          placeholder="Minutes:"
          onChange={(value) => setSelectedMinute(Number(value))}
          value={selectedMinute === null ? "" : selectedMinute.toString()}
          data={minutes.map(minute => ({
           value: minute.toString(),
           label: minute === 0 ? "00" : minute.toString(),
          }))}
          disabled={selectedHour === null}
        />
        {/* Sélection de la durée */}
        <Select
          id="lunchDuration"
          label="Durée:"
          placeholder="Durée:"
          onChange={(value) => setDuration(Number(value))}
          value={duration === null ? "" : duration.toString()}
          data={durations.map(dur => ({
            value: dur.toString(),
            label: dur >= 60
              ? `${Math.floor(dur / 60)}h${dur % 60 === 0 ? '' : dur % 60 + 'min'}`
              : `${dur}min`
          }))}
          disabled={selectedHour === null || selectedMinute === null}
        />
     
        <Select
  value={eventResourceId ? eventResourceId.toString() : ""}
  onChange={(value) => {
    console.log("Setting resourceId to:", value);
    setEventResourceId(Number(value) || 1);
  }}
  name="resourceId"
  placeholder="Room"
  data={[
    { value: "1", label: "Room A" },
    { value: "2", label: "Room B" },
  ]}
  required
  className="select w-full max-w-xs mt-6"
  leftSection={<MdOutlineBedroomChild size={16} />}
/>
<Avatar color="#4BA3D3" radius="sm"  ml={4} h={36} onClick={openedColorPickerlunch} mt={22}>
 <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 200 200"
 style={{width: "26px",height:"26px"}}
 >
   <path fill="#FF5178" d="M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z"></path><path fill="#FF9259" d="M49.982 13.408a99.999 99.999 0 00-36.595 36.61l51.968 29.99a40 40 0 0114.638-14.645l-30.01-51.955z"></path><path fill="#FFD23B" d="M13.386 50.02A100 100 0 000 100.025l60-.014a40 40 0 015.354-20.002L13.386 50.021z"></path><path fill="#89C247" d="M0 100a99.999 99.999 0 0013.398 50l51.961-30A40.001 40.001 0 0160 100H0z"></path><path fill="#49B296" d="M13.39 149.989a100.001 100.001 0 0036.599 36.607l30.006-51.958a39.99 39.99 0 01-14.639-14.643l-51.965 29.994z"></path><path fill="#2897B1" d="M49.989 186.596A99.995 99.995 0 0099.987 200l.008-60a39.996 39.996 0 01-20-5.362l-30.007 51.958z"></path><path fill="#3EC3FF" d="M100 200c17.554 0 34.798-4.621 50-13.397l-30-51.962A40 40 0 01100 140v60z"></path><path fill="#09A1E5" d="M150.003 186.601a100.001 100.001 0 0036.601-36.604l-51.962-29.998a40 40 0 01-14.641 14.641l30.002 51.961z"></path><path fill="#077CCC" d="M186.607 149.992A99.993 99.993 0 00200 99.99l-60 .006a39.998 39.998 0 01-5.357 20.001l51.964 29.995z"></path><path fill="#622876" d="M200 100c0-17.554-4.621-34.798-13.397-50l-51.962 30A39.997 39.997 0 01140 100h60z"></path><path fill="#962B7C" d="M186.597 49.99a99.994 99.994 0 00-36.606-36.598l-29.995 51.965a40 40 0 0114.643 14.64l51.958-30.006z"></path><path fill="#CB2E81" d="M149.976 13.384A99.999 99.999 0 0099.973 0l.016 60a40.001 40.001 0 0120.002 5.353l29.985-51.97z"></path></svg>
 </Avatar>
 <Modal opened={lunchColorPickeropened} onClose={closeColorPickerlunch}  size="auto" yOffset="18vh" xOffset={30} withCloseButton={false}>
         <ColorPicker  defaultValue="#FFFFFF"
      onChangeEnd={setChangeEndValuelunch} format="hex" swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} />
      <Group justify="center" mt={8}>
        {/* <Text > <b>{changeEndValue}</b> </Text> */}
        <Button variant="filled" w={"100%"} color={`${changeEndValuelunch}`} leftSection={<IconColorPicker stroke={1} size={18} />} onClick={() => { closeColorPickerlunch()}}>Registre</Button>
      </Group>
</Modal>
          </div>
          <Button
            className="w-full hover:bg-[#3799CE]/90"
            onClick={() => {
              close();
            }}
          >
            Annuler
          </Button>
          <Button
            className={selectedHour === null || selectedMinute === null ? "w-full disabled":"w-full hover:bg-[#3799CE]/90"}
            onClick={() => {
            addLunchEvent();
              close();
            }}
          >
              Confirmer
          </Button>
        </div>
        </div>
           </Modal>
           <Button
            size="xs"
            className="HoverButton"
            rightSection={<ChevronDown size={14} />}
            onClick={open}
          >
            La durée du déjeuner
          </Button>
               <div>
              </div>
             </div>
             <div className="flex gap-4">
                                 <Button
                                size="xs"
                                className="HoverButton"
                                rightSection={<ChevronDown size={14} />}
                                onClick={openedStartofwork}
                              >
                               Début des travaux
                              </Button>

                                 <ToolbarCalendarNav/>

                                 {/* Refresh button for appointments */}
                                 <Button
                                   variant="light"
                                   size="xs"
                                   onClick={fetchTodayAppointments}
                                   loading={loadingAppointments}
                                   leftSection={<IconRefresh size={14} />}
                                   color="#15aabf"
                                 >
                                   Refresh
                                 </Button>
                                 </div>

                                 {/* Error message for appointments */}
                                 {appointmentError && (
                                   <Alert color="red" title="Error" withCloseButton onClose={() => setAppointmentError(null)}>
                                     {appointmentError}
                                   </Alert>
                                 )}

                                 <Modal
                                opened={StartofworkOpened}
                                onClose={closeStartofwork}
                                withCloseButton={false}
                                size="auto"
                                 title={`Vous pouvez spécifier le début et la fin des travaux.`}
                              >
                               <div className="time-selector p-4 border rounded mb-4">
                               <div className="grid gap-2">
                                    <div className="flex gap-2 mb-2">
                                 <div className="grid gap-2">
                                          <div className="flex gap-2">
                                            <NumberInput
                                              label="Heure de début des travaux"
                                              placeholder="Select hour"
                                                  clampBehavior="strict"
                                                  step={1}
                                        min={0}
                                        max={23}
                                        value={minHour}
                                        onChange={(value) => setMinHour(value as number)}
                                            />
                                            <NumberInput
                                              label="Minute de début"
                                              placeholder="Select minute"
                                              clampBehavior="strict"
                                              step={5}
                                              min={0}
                                              max={59}
                                              value={minMinute}
                                              onChange={(value) => setMinMinute(value as number)}
                                            />
                                          </div>
                                        </div>
                                          <div className="grid gap-2">
                                  <div className="flex gap-2">
                                    <NumberInput
                                      label="Fin de l'heure de travail"
                                      placeholder="Select hour"
                                      clampBehavior="strict"
                                      step={1}
                                      min={0}
                                      max={23}
                                      value={maxHour}
                                      onChange={(value) => setMaxHour(value as number)}
                                      className="w-[50%]"
                                    />
                        <Select
                        label="Max Minute :"
                        value={step.toString()} // Ensure value is a string
                        onChange={(value) => {
                          if (value) {
                            setStep(parseInt(value, 10)); // Convert string to number
                          }
                        }}
                        placeholder="Select a step"
                        data={[
                          // { value: "5", label: "5" },
                          // { value: "10", label: "10" },
                          { value: "15", label: "15" },
                          { value: "20", label: "20" },
                          { value: "25", label: "25" },
                          { value: "30", label: "30" },
                          { value: "45", label: "45" },
                        ]}
                        className="w-[50%]"
                        defaultValue="15" // Set default to 15 minutes
                      />
                                  </div>

                                </div>
                              </div>
                              <Button
                                className="w-full hover:bg-[#3799CE]/90"
                                onClick={() => {
                                  closeStartofwork();
                                }}
                              >
                                Annuler
                              </Button>
                              <Button
                                className="w-full hover:bg-[#3799CE]/90"
                                onClick={() => {
                                  handleSave();
                                closeStartofwork();
                                }}
                              >
                                  Confirmer
                              </Button>
                            </div>
                            </div>
                               </Modal>
                               </div>
                             </div>
            {/* Custom Header */}
                      <Modal
                        opened={thirdModalOpened}
                        onClose={closeThirdModal}
                        title="Paramètre"
                      >
                        <div className="grid gap-2">
                          <div className="flex gap-2">
                            <NumberInput
                              label="Min Hour:"
                              placeholder="Select hour"
                              clampBehavior="strict"
                              step={1}
                              min={0}
                              max={23}
                              value={minHour}
                              onChange={(value) => setMinHour(value as number)}
                            />
                            <NumberInput
                              label="Min Minute:"
                              placeholder="Select minute"
                              clampBehavior="strict"
                              step={5}
                              min={0}
                              max={59}
                              value={minMinute}
                              onChange={(value) => setMinMinute(value as number)}
                            />
                          </div>
                          <Button
                            className="w-full hover:bg-[#3799CE]/90"
                            onClick={() => {
                              handleSave();
                              closeThirdModal();
                            }}
                          >
                            Save
                          </Button>
                        </div>
                      </Modal>
                      <Modal
                        opened={fourthModalOpened}
                        onClose={closeFourthModal}
                        title="Paramètre"
                      >
                        <div className="grid gap-2">
                          <div className="flex gap-2">
                            <NumberInput
                              label="Max Hour:"
                              placeholder="Select hour"
                              clampBehavior="strict"
                              step={1}
                              min={0}
                              max={23}
                              value={maxHour}
                              onChange={(value) => setMaxHour(value as number)}
                              className="w-[50%]"
                            />
                            <Select
                              label="Max Minute :"
                              value={step.toString()} // Ensure value is a string
                              onChange={(value) => {
                                if (value) {
                                  setStep(parseInt(value, 10)); // Convert string to number
                                }
                              }}
                              placeholder="Select a step"
                              data={[
                                { value: "5", label: "5" },
                                { value: "10", label: "10" },
                                { value: "15", label: "15" },
                                { value: "20", label: "20" },
                                { value: "25", label: "25" },
                                { value: "30", label: "30" },
                                { value: "45", label: "45" },
                              ]}
                              className="w-[50%]"
                            />
                          </div>
                          <Button
                            className="w-full hover:bg-[#3799CE]/90"
                            onClick={() => {
                              handleSave();
                              closeFourthModal();
                            }}
                          >
                            Save
                          </Button>
                        </div>
                      </Modal>
                      {/* <Header /> */}
            {/* <Header /> */}
            <SimpleBar className="simplebar-scrollable-y pb-6">
            <CalendarHeader
          label={moment(date).format("DD MMMM YYYY")}
          date={date}
          onNavigate={handleNavigate}
          // onAddToCalendar={handleAddToCalendar}
          onAddToCalendar={handleAddToCalendarAdapter}
        />
        <DragDropContext
        onDragEnd={handleDragEnd}
        >
            <div className="flex gap-4 mt-[5px]">
              <div className="bg-card text-card-foreground border-base-200 w-2/3 rounded-lg border shadow-sm ">
              <Group>
               {isSidebarVisible && (
        <div  style={{padding:"5px", width:226 , height: 600, borderRight: "1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4))"}}>
          {waitingList.length === 0 ? (
             <div className="mb-2 flex justify-end w-[216px]" >
            <Button
                // Added null check
                fullWidth
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowsExchange stroke={1.75}
                  style={{ width: rem(20) }}
                  onClick={(event) => {
                    event.preventDefault();
                    toggleSidebar(); // Toggle sidebar visibility
                  }}
                />
              }
            >
              <Text pr={8}>Loading events</Text>
              <Loader color="white" size="xs" type="dots" pt={6}/>
            </Button>
            </div>
          ) : (
            <div className="mb-2 flex justify-end w-[216px]" >
            <Button
                leftSection={waitingList?.length || 0} // Added null check
                fullWidth
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowsExchange stroke={1.75}
                  style={{ width: rem(20) }}
                  onClick={(event) => {
                    event.preventDefault();
                    toggleSidebar(); // Toggle sidebar visibility
                  }}
                />
              }
            >
              Nombre de patients
            </Button>
          </div>
          )}


           <div className={waitingList.length <=3 ?" w-[226px] z-40" : "h-[544px] w-[220px] "}>
           <SimpleBar className="simplebar-scrollable-y h-[calc(100%)] z-10">
           <Droppable droppableId="waitingList" direction="vertical">
{(provided) => (


  <div {...provided.droppableProps} ref={provided.innerRef}className="pr-3 " >
  {/*
   */}
  {waitingList.map((patient, index) => (
              <Draggable
                key={patient.id}
                draggableId={patient.id}
                index={index}
              >
                {(provided) => (

                  <Box
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}

                  >
             <Paper  className={`${
                 patient.eventType === "visit"
                 ? "border-l-4 border-l-[#34D1BF]"
                 : patient.eventType === "visitor-counter"
                 ? "border-l-4 border-l-[#F17105]"
                 : patient.eventType === "completed"
                 ? "border-l-4 border-l-[#3799CE]"
                 : patient.eventType === "diagnosis"
                 ? "border-l-4 border-l-[#ED0423]"
                 : "text-[var(--mantine-color-dark-0)]"
             }`}

               mb="sm"
                    p="4px"
                    withBorder >
                      <Group justify="space-between" mb="0px" >
                      <Group >
                      <Badge
                          size="sm"
                          variant="filled"
                          style={{
                            pointerEvents: "none",
                            padding: 0,
                            width: rem(20),
                            height: rem(20),
                            backgroundColor:`#3799CE`
                          }}

                        >
                           {index + 1}
                        </Badge>
                        <Text fz="xs" fw={700} className="uppercase" ml={-10}>
                        {patient.first_name.slice(0, 8)}&nbsp;{patient.last_name.slice(0, 3)}..
                        </Text>
                        </Group>

                          <Indicator
                          position="middle-end"
                              inline
                              size={12}
                              offset={0}
                              color="#3799CE"
                              mt={0}
                              withBorder
                              mr={4}
                              ml={10}
                            />
                        {/* <CloseButton mr={-9} mt={0} /> */}
                         <Menu
                        shadow="md"
                        position="left-start"
                        width={200}
                        transitionProps={{
                          transition: "rotate-right",
                          duration: 150,
                        }}
                      >
                        <Menu.Target>
                          <ActionIcon variant="subtle" aria-label="Menu options">
                            <IconDots size={18} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item
                            leftSection={
                              <FilePenLine size={14}  className="mt-1" color="#3799CE" />
                            }

                           onClick={() => openEditPatient(patient)}
                          >
                            Modifier
                          </Menu.Item>
                          <Menu.Item
                                leftSection={
                                 <IconEdit
                                   size={14}
                                   className="mt-1"
                                   color="#3799CE"
                                 />

                               }
                               onClick={() => {
                                 setViewPatient(patient);
                                 setInfoModalOpen(true);
                               }}
                               >
                                 Détails
                               </Menu.Item>
                          <Menu.Item
                            leftSection={
                              <Trash2 size={14} className="mt-1" color="red" />
                            }
                             onClick={() => setWaitingList(waitingList.filter(p => p.id !== patient.id))}
                                 color="red"
                          >
                            Supprimer
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                      </Group>
                      <Box className="flex" w={100}>
                        <List
                         spacing="2px"
                         size="xs"
                         center
                         style={{width:"100px"}}

                       >
                         <List.Item  icon={<FaUserDoctor size={12} strokeWidth={1.5}  color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end">{patient.docteur}</Text> </List.Item>
                         <List.Item icon={<ClipboardType size={10} strokeWidth={1.5}  color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end">{patient.typeConsultation}</Text></List.Item>
                         {/* <List.Item  icon={  <MdOutlineUpdate size={12} color={patient.color} />}> <Text size="xs" c="dimmed">{moment(patient.date).format("DD/MM/YYYY")} </Text></List.Item> */}

                       </List>
                       <List
                         spacing="2px"
                         size="xs"
                         center
                         style={{marginLeft:"4px",width:"100px",paddingLeft:"6px"}}
                         className={`${
                               patient.eventType === "visit"
                             ? "border-l-4 border-l-[#34D1BF]"
                             : patient.eventType === "visitor-counter"
                             ? "border-l-4 border-l-[#F17105]"
                             : patient.eventType === "completed"
                             ? "border-l-4 border-l-[#3799CE]"
                             : patient.eventType === "diagnosis"
                             ? "border-l-4 border-l-[#ED0423]"
                             : "text-[var(--mantine-color-dark-0)]"
                         }`}
                       >

                         <List.Item  icon={<MapPinHouse  size={14} strokeWidth={1.5}  color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end" >{patient.address.slice(0,9)}..</Text></List.Item>
                         <List.Item  icon={ <MessagesSquare  size={10} strokeWidth={1.5}  color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end">{patient.notes}...</Text></List.Item>
                       </List>
                       </Box>
                       </Paper>
                  </Box>
                )}
              </Draggable>
            ))}
            {/*
       </div>       */}
{provided.placeholder}
  </div>


)}
          </Droppable>

          </SimpleBar>
          </div>



   </div>
   )}


   <div className={isSidebarVisible ?  "h-[600px] w-[736px] -ml-4 ": "h-[600px]  w-full "}>
  <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">

  <div className="pr-3"
  //f={calendarRef}
  style={{ flex: 1 }}
  onDragOver={(e) => e.preventDefault()}//Drop={handleDrop}
  >

     <Droppable droppableId="calendar">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                    >

                        <DragAndDropCalendar
                        localizer={localizer}
                        events={appointments}
                        // startAccessor={(event) => (event as Appointment).start}
                        // endAccessor={(event) => (event as Appointment).end}
                         startAccessor="start"
                          endAccessor="end"
                        messages={messages}
                        onSelectSlot={handleSlotSelect}
                        selectable
                        defaultView={Views.DAY}
                        views={[Views.DAY]}
                        onEventDrop={onEventDrop}
                        eventPropGetter={eventStyleGetter}
                        dayLayoutAlgorithm="no-overlap"
                        slotPropGetter={() => ({
                          style: {
                            maxHeight: '26.5px', // Adjust this value as needed for better display
                            height: "26px !important",
                          },
                        })}
                        //titleAccessor={(Appointment: Appointment) => `${Appointment.first_name} ${Appointment.last_name}`}
                        components={{
                          event: EventComponent,
                          toolbar: () => null,
                          timeSlotWrapper: TimeSlotWrapper,
                        }}


                        defaultDate={new Date()}
                        step={step}
                        min={minTime}
                        max={maxTime}
                        timeslots={1}
                        resources={resources}
                        resourceIdAccessor={(resource: object) => (resource as Resource).id}
                    // resourceIdAccessor="id"
                         resourceTitleAccessor={(resource: object) => (resource as Resource).title}
                        //resourceTitleAccessor="title"
                        resizable

                      />


                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
  </div>
  </SimpleBar>
  </div>
  </Group>


  <div className="border-base-200 rounded-b-lg border-t bg-[--content-background] px-[24px] py-[20px] mb-[30px]">
 
    <ul className="flex flex-wrap gap-x-4 gap-y-2 text-[var(--mantine-color-dark-0)]">
      {[
        { label: "Visite de malade", color: "disk-teal", value: "Visite de malade", eventType: "visit" },
        { label: "Visitor Counter", color: "disk-orange", value: "Visitor Counter", eventType: "visitor-counter" },
        { label: "Completed", color: "disk-azure", value: "Completed", eventType: "completed" },
        { label: "Re-diagnose", color: "disk-red", value: "Re-diagnose", eventType: "diagnosis" },
      ].map(({ label, color, value, eventType }) => (
        <li
          key={label}
          className={`flex items-center gap-2 text-xs uppercase cursor-pointer hover:opacity-80 ${
            type === value ? 'font-bold' : ''
          }`}
          onClick={() => {
            setType(value);
            setEventType(eventType as EventType);
          }}
        >
          <span className={`${color} relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white`} />
          {label}
        </li>
      ))}
    </ul>
  </div>
  </div>
      <div className="card border-base-100 bg-base-100 w-1/3 pb-2 absolute top-0 right-0 ">
      <div>
      <Tabs variant="none" value={value} onChange={setValue}>
      <Tabs.List ref={setRootRef} className={classes.list}>
      <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] flex w-full justify-between items-center">
      <span className="text-sm font-medium capitalize flex gap-2">
        <IconStethoscope stroke={1.25} />
        Visites Actives
      </span>

      <div className="flex gap-2">
      <Tabs.Tab
    value="1"
    ref={setControlRef('1')}
    styles={{
      tab: {
        zIndex: 1,
        fontWeight: 500,
        transition: 'all 100ms ease',
        color: '#ffffff',
        padding: '8px 12px',
        '&[dataActive="true"]': {
          color: '#3799CE',
          backgroundColor: 'white',
          borderRadius: '4px',
          borderBottom: '2px solid #3799CE',
        },
      },
    }}
  >
    Room A
  </Tabs.Tab>
        <Tabs.Tab
          value="2"
          ref={setControlRef('2')}
          styles={{
            tab: {
              zIndex: 1,
              fontWeight: 500,
              transition: 'all 100ms ease',
              color: '#ffffff',
              padding: '8px 12px',
              '&[dataActive="true"]': {
                color: '#3799CE',
                backgroundColor: 'white',
                borderRadius: '4px',
                borderBottom: '2px solid #3799CE',
              },
            },
          }}
        >
          Room B
        </Tabs.Tab>
      </div>
  </div>

  <FloatingIndicator
    target={value ? controlsRefs[value] : null}
    parent={rootRef}
    styles={{
      root: {
        color: 'red',
        backgroundColor: '#15AABF',
        borderRadius: 'var(--mantine-radius-md)',
        borderBottom: '2px solid #3799CE',
        boxShadow: 'var(--mantine-shadow-lg)',
      },
    }}
  />
        </Tabs.List>
        <Tabs.Panel value="1">
        <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {activeVisits.length === 0  ? (
          <Text>Loading events...</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-between">
        <Tooltip
          label="Nombre de patients"
          withArrow
          position="bottom"
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Group className="bg-[--content-background]">
            NP:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >
              {/* {activeVisits.length}  */}
              {activeVisits.filter(visit => visit.resourceId === 1).length}
            </Badge>
          </Group>
        </Tooltip>
        <Tooltip
          label="Résultats du diagnostic"
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
          position="bottom"
        >
          <Group className="bg-[--content-background]">
            RDD:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >

      {activeVisits
        .filter(event =>
          event.resourceId === 1 &&
          event.eventType === "visitor-counter"
        ).length}

            </Badge>
             
          </Group>
        </Tooltip>
        <Group>
        
      <Menu shadow="md" width={200}>
      <Menu.Target>
           <Tooltip label="Filter par motifs">
      <Icon path={mdiBookmarkOutline} size={1} color="#15AABF"/>
      </Tooltip>
      </Menu.Target>
      <Menu.Dropdown>
     <SimpleBar className="simplebar-scrollable-y pb-6">
        <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Tous
        </Menu.Item>
        <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
          Consultation
        </Menu.Item>
        <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Contrôle
        </Menu.Item>
	   </SimpleBar>
      </Menu.Dropdown>
    </Menu>
       <Tooltip label="Ajouter entrée">
      <Icon path={mdiSeatLegroomExtra} size={1} color="#15AABF" 
	  onClick={() => openRendezVous()}
	  />
      </Tooltip>
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Button

          leftSection={
            activeVisits
            .filter(event => event.resourceId === 1)
            .filter(event => activeIds.includes(Number(event.id)))
            .length
          }
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowRight
                  style={{ width: rem(18) }}
                />
              }
            >
              NPD
            </Button>
          </Menu.Target>

          <Menu.Dropdown >
            <Tooltip
              label="Nombre de patients diagnostiqués"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
              position="top"
            >
              <Menu.Label className="bg-[--content-background]" >
                diagnostiqués 1
              </Menu.Label>
            </Tooltip>
            <div className={activeVisits.filter(visit => visit.resourceId === 1).length <= 5 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
             <div className="pr-3" >

   <Menu.Divider />
            {activeVisits.length > 0 ? (
              // activeVisits.map((event, index) => {
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        onClick={() => {

                          setActiveIds(
                            activeIds.filter(
                              (id) => id !== Number(event.id),
                            ),
                          );
                        }}
                      >
                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}
                           bg={"green"}
                         >
                           {/* {event.id} */}
                           {/* {activeVisits.length}  */}
                           {index + 1}
                         </Badge>
                         <Text size="xs" c="green" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name.slice(0, 8)}&nbsp;
                         {event.last_name.slice(0, 8)}
                         </Text>
                         <IconArrowBackUpDouble stroke={1.5} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
     <Menu.Divider />
            </div>
          </SimpleBar>
      </div>
          </Menu.Dropdown>
        </Menu>
        </Group>
      </div>
           <DragDropContext
    onDragEnd={({ destination, source }) =>
      handlers.reorder({ from: source.index, to: destination?.index || 0 })
    }
  >
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {activeVisits.length > 0 ? (
          <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="dnd-list" direction="vertical">
            {(provided) => (
              <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?" w-full " : "h-[180px] w-full "}>
               <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div {...provided.droppableProps} ref={provided.innerRef}
               className="pr-3 ">
          {activeVisits.length > 0 ? (
             activeVisits.filter(visit => visit.resourceId === 1).map((visit, index) => (
              <Draggable
                key={`visit-${visit.id}-${index}`}
                draggableId={visit.id.toString()}
                index={index}
              >
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || visit.checkedListedattente,
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    
                  >
                    <>
                      <Card
                        className={`my-3  ${classes.card} ${
                          visit.type === "visit"
                            ? "border-l-4 border-l-[#34D1BF]"
                            : visit.type === "visitor-counter"
                            ? "border-l-4 border-l-[#F17105]"
                            : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                            ? "border-l-4 border-l-[#ED0423]"
                            : "text-[var(--mantine-color-dark-0)]"
                        }`}
                        shadow="sm"
                        radius="md"
                        style={{
                          cursor: "grab",
                          borderLeftWidth: "2px",
                        }}
                        mt={3}
                      >
                        <Card.Section p="xs" >
                          <Flex align="center" justify="space-between">
                            <Group w="62%">
                              <Badge size="sm" variant="filled" style={{ pointerEvents: "none", padding: 0, width: 20, height: 20 }}>
                                {index + 1} {/* Display counter starting from 1 */}
                              </Badge>
                             
                              <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                              <TimeValue value="18:45:34" />
                               <span className="capitalize">{visit.last_name.slice(0, 6)} &nbsp;{visit.first_name.slice(0, 8)} </span>
                                 <Group gap="5px">
                            <PhoneCall size={12} strokeWidth={1.5} color="#868e96" />
                            <Text c="dimmed" size="sm" >
                              {visit.phone_numbers}
                            </Text>
                          </Group>
                            </Group>
                            {/* Right Section */}
                            <Group justify="flex-end" gap="6px">
                              <Group  gap="2px">
                              <Indicator
                                  disabled={!activeEvent_Ids.has(Number(visit.id))}
                                  processing
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="red"
                                  mt={2}
                                  withBorder
                                />
                                <Indicator
                                  disabled={activeEvent_Ids.has(Number(visit.id))}
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="#3799CE"
                                  mt={2}
                                  mr={6}
                                  withBorder
                                />
                                </Group>
                              <Switch
                                checked={activeEvent_Ids.has(Number(visit.id))}
                                onChange={(e) => {
                                  if (e.currentTarget.checked) {
                                    addEventId(Number(visit.id));
                                  } else {
                                    removeEventId(Number(visit.id));
                                  }
                                }}
                                color="teal"
                                size="sm"
                                ml={6}
                                thumbIcon={
                                  activeEvent_Ids.has(Number(visit.id)) ? (
                                    <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                                  ) : (
                                    <IconX size={12} color="var(--mantine-color-red-6)" stroke={3} />
                                  )
                                }
                              />
                              <Tooltip label="Accés chez le médecin">
                                <IconDental size={18}  color= "rgb(3, 169, 244)"/>
                                </Tooltip>
                                 <Tooltip label="Fiche patient">
                                  <Text c="#ED0423" size="sm" >
                                   {/* {visit.phone_numbers} */}
                                   1 000,00 DH
                                  </Text>
                                </Tooltip>
                                 
                                {/* <Icon path={mdiAccountArrowUp} size={1} /> */}
                            </Group>
                            <Menu
                              shadow="md"
                              position="left-start"
                              width={250}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <IconDotsVertical size={18} />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs  
                                  <Menu.Item
                                    leftSection={
                                      <ShieldCheck
                                        size={14}
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountEdit}  size={1} className="mt-1" color="#15AABF" />
                                  }
                                //  onClick={() => openedFichepatient()}
                                >
                                  Modifier l&apos;entrée
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountConvert}  size={1} className="mt-1" color="#15AABF" />
                                  }
                                  // onClick={() => openedFichepatient()}
                                >
                                  Annuler l&apos;entrer
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountFileText}  size={1} className="mt-1" color="#15AABF" />
                                  }
                                  onClick={() => openedFichepatient()}
                                >
                                  Fiche patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarAccount}  size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Nouveau rendez-vous
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountArrowLeft} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                  Afficher la dernière visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountFile} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                  Ajouter résultat biologique
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShareAll} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                     <Icon path={mdiShareAll} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                   Créer DICOM ordre
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountArrowDown} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                  Accés chez le médecin
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountCash} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                 Réglement à l&apos;avance
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountCash} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                 Régler un plan de traitement
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountCash} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Recettes
                                </Menu.Item>
                                   <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCashMultiple} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  État financier
                                </Menu.Item>
                                <Menu.Divider />
                                    <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowLeft} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Sortie du patient
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Trash2 size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                          </Flex>
                        </Card.Section>
                      </Card>
                    </>
                  </div>
                )}
              </Draggable>
            ))
          ) : (
            <Text>No results found</Text>
          )}
                {provided.placeholder}
              </div>
              </SimpleBar>
              </div>
            )}
          </Droppable>
        </DragDropContext>
        ) : (
          <Text>No results found</Text>
        )}
        {provided.placeholder}
      </div>
    )}
  </Droppable>
  </DragDropContext>

                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                  >
                    Add New Event
                  </Button>
                </div>
       </Container>
          <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] ml-6" >
          <span className="text-sm font-medium capitalize">
          <span className="flex gap-2 text-sm font-medium capitalize">
          <svg
          xmlns="http://www.w3.org/2000/svg"
          width="2em"
          height="1.5em"
          viewBox="0 0 20 20"
          >
          <path
          fill="none"
          stroke="currentColor"
          d="M18 3v2h2M1 19.5h7M7.5 14V6.5H6.328a3 3 0 0 0-2.906 2.255L1.5 16.25v.25h9V18c0 1.5 0 2.5.75 4c0 0 .75 1.5 1.75 1.5m5-14a4.5 4.5 0 1 1 0-9a4.5 4.5 0 0 1 0 9Zm-10.65-5s-1.6-1-1.6-2.25a1.747 1.747 0 1 1 3.496 0C9.246 3.5 7.65 4.5 7.65 4.5z"
          ></path>
          </svg>
          Salle D&apos;attente
          </span>
          </span>
          </div>
          <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {activeVisits.length === 0 ? (
          <Text>Loading events...</Text>
          ) : (
          <>
        
          <div className="mb-2 flex justify-between">
            <Tooltip
          label="Nombre de patients"
          withArrow
          position="bottom"
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Group className="bg-[--content-background]">
            NP:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >
              {/* {activeVisits.length}  */}
              {activeVisits.filter(visit => visit.resourceId === 1).length}
            </Badge>
          </Group>
        </Tooltip>
        <Tooltip
          label="Résultats du diagnostic"
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
          position="bottom"
        >
          <Group className="bg-[--content-background]">
            RDD:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >

      {activeVisits
        .filter(event =>
          event.resourceId === 1 &&
          event.eventType === "visitor-counter"
        ).length}

            </Badge>
             
          </Group>
        </Tooltip>
             <Group className=" flex justify-end ">
            <Menu shadow="md" width={200} >
      <Menu.Target>
           <Tooltip label="Filter par motifs">

      <Icon path={mdiBookmarkOutline} size={1} color="#15AABF"/>
      </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
	  
     <SimpleBar className="simplebar-scrollable-y pb-6">
        <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Tous
        </Menu.Item>
        <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
          Consultation
        </Menu.Item>
        <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Contrôle
        </Menu.Item>
		
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
Chirurgie/Paro
        </Menu.Item>
		
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Composite
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
Depose Sutures
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         1er Consultation
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Devis
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Endodontie
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Formation
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Implantologie
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Orthodontie
        </Menu.Item>
		
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         PA ES Chassis/Monta
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         PA Pose
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         PC TP EMP
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         PC ESS Armature
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         PC Scellement
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Urgent
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Detartrage
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Obturation Canalaire
        </Menu.Item>
		
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         polissage
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Changement D'élastique
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Collage
        </Menu.Item>
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Contention
        </Menu.Item>
		
		 <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Echographie
        </Menu.Item>
       
	    <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         Contention
        </Menu.Item>
       
	    <Menu.Item leftSection={<Icon path={mdiCheck} size={1} />}>
         comp
        </Menu.Item>
	   </SimpleBar>
      </Menu.Dropdown>
          </Menu>
            <Tooltip label="Ajouter entrée">
            <Icon path={mdiAccountBoxPlusOutline} size={1} color="#15AABF" 
          onClick={() => openRendezVous()}
          />
            </Tooltip>
          
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <Button
              leftSection={
                activeVisits
                .filter(event => event.resourceId === 1) // Filter for Room B
                .filter(event => activeIds.includes(Number(event.id))) // Filter for active events
                .length
              }
                  bg={"#3799CE"}
                  classNames={classes}
                  radius="md"
                  rightSection={
                    <IconArrowRight
                      style={{ width: rem(18) }}
                    />
                  }
                >
                  NPD
                </Button>
              
              </Menu.Target>

              <Menu.Dropdown>
                <Tooltip
                  label="Nombre de patients diagnostiqués"
                  withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  position="top"
                >
                  <Menu.Label className="bg-[--content-background]">
                    diagnostiqués 2
                  </Menu.Label>
                </Tooltip>
                <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>

                <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div className="pr-3">
            {/* Render the list of events */}
   <Menu.Divider />
            {activeVisits.length > 0 ? (
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        // onClick={() => {
                        //   setActiveIds(
                        //     activeIds.filter(
                        //       (id) => id !== Number(event.id),
                        //     ),
                        //   );
                        // }}
                      >

                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}

                           bg={"#3799CE"}
                         >
                           {/* {event.id}  */}
                            {/* {activeVisits.length}  */}
                            {index + 1}
                         </Badge>
                         <Text size="xs" c="#3799CE" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name.slice(0, 8)}&nbsp;
                         {event.last_name.slice(0, 8)}
                         </Text>
                         <IconWallpaper stroke={1.25} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
            <Menu.Divider />
            </div>
            </SimpleBar>
            </div>
              </Menu.Dropdown>
            </Menu>
            </Group>
          </div>

           <DragDropContext
    onDragEnd={({ destination, source }) =>
      handlers.reorder({ from: source.index, to: destination?.index || 0 })
    }
  >
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {activeVisits.length > 0 ? (
          <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="dnd-list" direction="vertical">
            {(provided) => (
              <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?" w-full" : "h-[226px] w-full"}>
               <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div {...provided.droppableProps} ref={provided.innerRef}
               className="pr-3 ">
          {activeVisits.length > 0 ? (
            activeVisits.filter(visit => visit.resourceId === 1).map((visit, index) => (
              <Draggable key={visit.id} draggableId={visit.id.toString()} index={index}>
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || visit.checkedListedattente,
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>
                    <Card
                        className={`my-3  ${classes.card} ${
                          visit.type === "visit"
                            ? "border-l-4 border-l-[#34D1BF]"
                            : visit.type === "visitor-counter"
                            ? "border-l-4 border-l-[#F17105]"
                            : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                            ? "border-l-4 border-l-[#ED0423]"
                            : "text-[var(--mantine-color-dark-0)]"
                        }`}
                        shadow="sm"
                        radius="md"
                        style={{
                          cursor: "grab",
                          borderLeftWidth: "2px",
                        }}
                        mt={3}
                      >
                        <Card.Section p="xs" >
                          <Flex align="center" justify="space-between">
                            <Group w="68%">
                              <Badge size="sm" variant="filled" style={{ pointerEvents: "none", padding: 0, width: 20, height: 20 }}>
                                {index + 1} {/* Display counter starting from 1 */}
                              </Badge>
                             
                              <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                              <Group gap="5px">
                            <AlarmClock size={12} strokeWidth={1.5} color="#868e96"/>
                            <Group gap="4px" color="#868e96" >
                              <Text c="dimmed" size="xs">{formatTime(visit.start)}</Text>
                              <BiSolidArrowFromLeft color="#3799CE"/>
                              <Text c="dimmed" size="xs">{formatTime(visit.end)}</Text>
                              </Group>
                        
                          </Group>
                               <span className="capitalize">{visit.last_name.slice(0, 6)} &nbsp;{visit.first_name.slice(0, 8)} </span>
                             
                            </Group>
                            {/* Right Section */}
                            <Group justify="flex-end" gap="6px">
              
                              <Tooltip label="Salle de consultation">
                                <Badge color="red"  radius="sm" size="sm">FTL</Badge>
                                </Tooltip>
                                 <Tooltip label="Salle d'attente">
                                <Badge  color="cyan" radius="sm"  size="sm">SLT</Badge>
                                </Tooltip>
                                 <Tooltip label="Fiche patient">
                                <Icon path={mdiAccountDetails} size={1} color="rgb(3, 169, 244) "
                                onClick={() => {
                                  setShowViewModal(true);
                                }}
                                />
                                </Tooltip>
                                 <Tooltip label="Accés chez le médecin">
                                <Icon path={mdiAccountArrowDown} size={1} color= "rgb(3, 169, 244)"/>
                                </Tooltip>
                                {/* <Icon path={mdiAccountArrowUp} size={1} /> */}
                            
                            </Group>
                            <Menu
                              shadow="md"
                              position="left-start"
                              width={250}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <IconDotsVertical size={18} />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs  
                                
                                  <Menu.Item
                                    leftSection={
                                      <ShieldCheck
                                        size={14}
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountEdit}  size={1} className="mt-1" color="#15AABF" />
                                  }
                                 onClick={() => {setShowEditModal(true)}}
                                >
                                  Modifier l&apos;entrée
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountConvert}  size={1} className="mt-1" color="#15AABF" />
                                  }
                                  // onClick={() => openedFichepatient()}
                                >
                                  Annuler l&apos;entrer
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountFileText}  size={1} className="mt-1" color="#15AABF" />
                                  }
                                  onClick={() => openedFichepatient()}
                                >
                                  Fiche patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarAccount}  size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Nouveau rendez-vous
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountArrowLeft} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                  Afficher la dernière visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountFile} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                  Ajouter résultat biologique
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShareAll} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                     <Icon path={mdiShareAll} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                   Créer DICOM ordre
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountArrowDown} size={1} className="mt-1" color="#15AABF" />
                                  }
                                 
                                >
                                  Accés chez le médecin
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountCash} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                 Réglement à l&apos;avance
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountCash} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                 Régler un plan de traitement
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountCash} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Recettes
                                </Menu.Item>
                                   <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCashMultiple} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  État financier
                                </Menu.Item>
                                <Menu.Divider />
                                    <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowLeft} size={1} className="mt-1" color="#15AABF" />
                                  }
                                  
                                >
                                  Sortie du patient
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Trash2 size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                  
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                          </Flex>
                        </Card.Section>
                      </Card>
                 
                  <Modal opened={VisitesActivesDeleteOpened} onClose={closeVisitesActivesDelete} withCloseButton={false}>
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                  Êtes-vous sûr de vouloir supprimer l&apos;événement ?

                </Alert>
                <Group justify="space-between" mt="md" mb="xs">
                <Button
                variant="filled"
                size="xs"
                radius="lg"
                color="red"
                onClick={() => {
                  // Use the event's actual ID, not counterId
                  const eventId = Number(visit.id);
                  if (!isNaN(eventId)) {
                    deleteVisits(eventId);
                    closeVisitesActivesDelete();
                  } else {
                    console.error("Invalid event ID:", visit.id);
                    notifications.show({
                      title: 'Error',
                      message: 'Could not delete event: Invalid ID',
                      color: 'red',
                    });
                  }
                }}
              >
                Oui
              </Button>
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="lime.4"
                    onClick={closeVisitesActivesDelete}
                  >
                    Non
                  </Button>
                </Group>
            </Modal>
                    </>
                  </div>
                )}
              </Draggable>
            ))
          ) : (
            <Text>No results found</Text>
          )}
                {provided.placeholder}
              </div>
              </SimpleBar>
              </div>
            )}
          </Droppable>
        </DragDropContext>
        ) : (
          <Text>No results found</Text>
        )}
        {provided.placeholder}
      </div>
    )}
  </Droppable>
  </DragDropContext>
                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                  >
                    Add New Event
                  </Button>
                </div>
           </Container>
        </Tabs.Panel>
        <Tabs.Panel value="2">
        <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {/* {activeVisits.length === 0  ? ( */}
          {activeVisits.filter(visit => visit.resourceId === 2).length === 0 ? (
          <Text>Loading events...</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-between">
        <Tooltip
          label="Nombre de patients"
          withArrow
          position="bottom"
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Group className="bg-[--content-background]">
            NP:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >
              {/* {activeVisits.length}  */}
              {/* {Object.values(eventResourceId).filter(value => value === true).length}  */}

              {activeVisits.filter(visit => visit.resourceId === 2).length}
            </Badge>
          </Group>
        </Tooltip>
        <Tooltip
          label="Résultats du diagnostic"
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
          position="bottom"
        >
          <Group className="bg-[--content-background]">
            RDD:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >
              {activeVisits
        .filter(event =>
          event.resourceId === 2 &&
          event.eventType === "visitor-counter"
        ).length}
            </Badge>
          </Group>
        </Tooltip>
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Button
             leftSection={
              activeVisits.filter(event => event.resourceId === 2)
              .filter(event => activeIds.includes(Number(event.id)))
              .length
            }
            // {!activeIds.includes(Number(visit.id))
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowRight
                  style={{ width: rem(18) }}
                />
              }
            >
              NPD
            </Button>
          </Menu.Target>

          <Menu.Dropdown >
            <Tooltip
              label="Nombre de patients diagnostiqués"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
              position="top"
            >
              <Menu.Label className="bg-[--content-background]" >
                diagnostiqués 1
              </Menu.Label>
            </Tooltip>
            <div className={activeVisits.filter(visit => visit.resourceId === 2).length <= 3 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>

            {/* style={{maxHeight: "120px", overflowY: 'auto'}}> */}
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
             <div className="pr-3" >
             {/* styles={{ dropdown: { maxHeight: 200, overflowY: 'auto' } }} */}
   {/* Render the list of events */}
   <Menu.Divider />
            {activeVisits.length > 0 ? (
              activeVisits.filter(visit => visit.resourceId === 2).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        onClick={() => {
                          setActiveIds(
                            activeIds.filter(
                              (id) => id !== Number(event.id),
                            ),
                          );
                        }}
                      >
                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}
                           bg={"green"}
                         >
                           {/* {event.id} */}
                           {/* {activeVisits.length}  */}
                           {index + 1}
                         </Badge>
                         <Text size="xs" c="green" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name.slice(0, 8)}&nbsp;
                         {event.last_name.slice(0, 8)}
                         </Text>
                         <IconArrowBackUpDouble stroke={1.5} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
     <Menu.Divider />
            </div>
          </SimpleBar>
      </div>
          </Menu.Dropdown>
        </Menu>
      </div>
           <DragDropContext
    onDragEnd={({ destination, source }) =>
      handlers.reorder({ from: source.index, to: destination?.index || 0 })
    }
  >
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {activeVisits.length > 0 ? (
          <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="dnd-list" direction="vertical">
            {(provided) => (
     <div className={activeVisits.filter(visit => visit.resourceId === 2).length <= 3 ?"w-[440px]" : "h-[180px] w-[440px]"}>
               <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div {...provided.droppableProps} ref={provided.innerRef}
               className="pr-3 ">
          {activeVisits.length > 0 ? (
            activeVisits.filter(visit => visit.resourceId === 2).map((visit, index) => (
              <Draggable
                key={`visit-${visit.id}-${index}`}
                draggableId={visit.id.toString()}
                index={index}
              >
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || visit.checkedListedattente,
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>
                      <Card
                        className={`my-3 ${classes.card} ${
                          visit.type === "visit"
                            ? "border-l-4 border-l-[#34D1BF]"
                            : visit.type === "visitor-counter"
                            ? "border-l-4 border-l-[#F17105]"
                            : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                            ? "border-l-4 border-l-[#ED0423]"
                            : "text-[var(--mantine-color-dark-0)]"
                        }`}
                        shadow="sm"
                        radius="md"
                        style={{
                          cursor: "grab",
                          borderLeftWidth: "2px",
                        }}
                        mt={3}
                      >
                        <Card.Section p="xs">
                          <Flex align="center" justify="space-between">
                            <Group w="70%">
                              <Badge size="sm" variant="filled" style={{ pointerEvents: "none", padding: 0, width: 20, height: 20 }}>
                                {index + 1} {/* Display counter starting from 1 */}
                              </Badge>
                              <span className="capitalize">{visit.last_name.slice(0, 6)}&nbsp; {visit.first_name.slice(0, 8)} </span>
                              <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                                <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                            </Group>
                            {/* Right Section */}
                            <Group>
                              <Switch
                                checked={activeEvent_Ids.has(Number(visit.id))}
                                onChange={(e) => {
                                  if (e.currentTarget.checked) {
                                    addEventId(Number(visit.id));
                                  } else {
                                    removeEventId(Number(visit.id));
                                  }
                                }}
                                color="teal"
                                size="sm"
                                thumbIcon={
                                  activeEvent_Ids.has(Number(visit.id)) ? (
                                    <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                                  ) : (
                                    <IconX size={12} color="var(--mantine-color-red-6)" stroke={3} />
                                  )
                                }
                              />
                              <Group>
                                <Indicator
                                  disabled={!activeEvent_Ids.has(Number(visit.id))}
                                  processing
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="red"
                                  mt={2}
                                  withBorder
                                />
                                <Indicator
                                  disabled={activeEvent_Ids.has(Number(visit.id))}
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="#3799CE"
                                  mt={2}
                                  withBorder
                                />
                              </Group>
                            </Group>
                            <Menu
                              shadow="md"
                              position="left-start"
                              width={200}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <IconDots size={18} />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs
                                  <Menu.Item
                                    leftSection={
                                      <ShieldCheck
                                        size={14}
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}
                               <Menu.Item
                                  leftSection={
                                    <FaUserInjured size={14} className="mt-1" color="#15aabf" />
                                  }
                                  onClick={() => openedFichepatient()}
                                >
                                  Fiche patient
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Trash2 size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                          </Flex>
                        </Card.Section>
                      </Card>
                    </>
                  </div>
                )}
              </Draggable>
            ))
          ) : (
            <Text>No results found</Text>
          )}
                {provided.placeholder}
              </div>
              </SimpleBar>
              </div>
            )}
          </Droppable>
        </DragDropContext>
        ) : (
          <Text>No results found</Text>
        )}
        {provided.placeholder}
      </div>
    )}
  </Droppable>
  </DragDropContext>

                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 hover:bg-[#3799CE]/90"
                  >
                    Add New Event
                  </Button>
                </div>
       </Container>
          <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] ml-6" >
          <span className="text-sm font-medium capitalize">
          <span className="flex gap-2 text-sm font-medium capitalize">
          <svg
          xmlns="http://www.w3.org/2000/svg"
          width="2em"
          height="1.5em"
          viewBox="0 0 20 20"
          >
          <path
          fill="none"
          stroke="currentColor"
          d="M18 3v2h2M1 19.5h7M7.5 14V6.5H6.328a3 3 0 0 0-2.906 2.255L1.5 16.25v.25h9V18c0 1.5 0 2.5.75 4c0 0 .75 1.5 1.75 1.5m5-14a4.5 4.5 0 1 1 0-9a4.5 4.5 0 0 1 0 9Zm-10.65-5s-1.6-1-1.6-2.25a1.747 1.747 0 1 1 3.496 0C9.246 3.5 7.65 4.5 7.65 4.5z"
          ></path>
          </svg>
          Salle D&apos;attente
          </span>
          </span>
          </div>
          <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {activeVisits.length === 0 ? (
          <Text>Loading events...</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-end">
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <Button
              // .filter(visit => visit.resourceId === 2)
                  leftSection={
                    activeVisits
                    .filter(event => event.resourceId === 2) // Filter for Room B
                    .filter(event => activeIds.includes(Number(event.id))) // Filter for active events
                    .length

                  }
                  bg={"#3799CE"}
                  classNames={classes}
                  radius="md"
                  rightSection={
                    <IconArrowRight
                      style={{ width: rem(18) }}
                    />
                  }
                >
                  NPD
                </Button>
              </Menu.Target>

              <Menu.Dropdown>
                <Tooltip
                  label="Nombre de patients diagnostiqués"
                  withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  position="top"
                >
                  <Menu.Label className="bg-[--content-background]">
                    diagnostiqués 2
                  </Menu.Label>
                </Tooltip>
                <div className={activeVisits.filter(visit => visit.resourceId === 2).length <=3 ?" w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>

                <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div className="pr-3">
            {/* Render the list of events */}
   <Menu.Divider />
            {activeVisits.length > 0 ? (
              activeVisits.filter(visit => visit.resourceId === 2).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        // onClick={() => {
                        //   setActiveIds(
                        //     activeIds.filter(
                        //       (id) => id !== Number(event.id),
                        //     ),
                        //   );
                        // }}
                      >

                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}

                           bg={"#3799CE"}
                         >
                           {/* {event.id}  */}
                            {/* {activeVisits.length}  */}
                            {index + 1}
                         </Badge>
                         <Text size="xs" c="#3799CE" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name.slice(0, 8)}&nbsp;
                         {event.last_name.slice(0, 8)}
                         </Text>

                         <IconWallpaper stroke={1.25} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
            <Menu.Divider />
            </div>
            </SimpleBar>
            </div>
              </Menu.Dropdown>
            </Menu>
          </div>
           <DragDropContext
    onDragEnd={({ destination, source }) =>
      handlers.reorder({ from: source.index, to: destination?.index || 0 })
    }
  >
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {activeVisits.length > 0 ? (
          <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="dnd-list" direction="vertical">
            {(provided) => (
            <div className={activeVisits.filter(visit => visit.resourceId === 2).length <=3 ?" w-[440px]" : "h-[226px] w-[440px]"}>

               <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div {...provided.droppableProps} ref={provided.innerRef}
               className="pr-3 ">
          {activeVisits.length > 0 ? (
            activeVisits.filter(visit => visit.resourceId === 2).map((visit, index) => (
              <Draggable key={visit.id} draggableId={visit.id.toString()} index={index}>
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || visit.checkedListedattente,
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>

                  <Card
                    className={`my-3 ${classes.card} ${

                      visit.type === "visit"
                        ? "border-l-4 border-l-[#34D1BF]"
                        : visit.type === "visitor-counter"
                          ? "border-l-4 border-l-[#F17105]"
                          : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                              ? "border-l-4 border-l-[#ED0423]"
                              : "text-[var(--mantine-color-dark-0)]"
                    }`}
                    shadow="sm"
                    radius="md"
                    style={{
                      cursor: "grab",
                      borderLeftWidth: "2px",
                    }}
                    mt={3}
                  >
                    <Card.Section p="sm">
                      <Group gap="md">
                        <Text size="xs"  component="span">
                          <Group gap="5px">
                          <Badge
                            size="sm"
                            variant="filled"
                            style={{
                              pointerEvents: "none",
                              padding: 0,
                              width: rem(20),
                              height: rem(20),
                            }}
                          >
                            {/* {event.id}  */}
                            {/* {counterId} */}
                            {index + 1}
                          </Badge>
                            <Text  size="xs" fw={700} >
                            {visit.last_name.slice(0, 8)}&nbsp;
                            {visit.first_name.slice(0, 8)}

                            </Text>
                          </Group>
                        </Text>
                        <Group gap="5px">
                            <PhoneCall size={12} strokeWidth={1.5} color="#868e96" />
                            <Text c="dimmed" size="sm" >
                              {visit.phone_numbers}
                            </Text>
                          </Group>
                          <Group gap="5px">
                            <AlarmClock size={12} strokeWidth={1.5} color="#868e96"/>
                            <Group gap="4px" color="#868e96" >
                              <Text c="dimmed" size="xs">{formatTime(visit.start)}</Text>
                              <BiSolidArrowFromLeft color="#3799CE"/>
                              <Text c="dimmed" size="xs">{formatTime(visit.end)}</Text>
                              </Group>
                          </Group>
                      <MdOutlineAutoDelete size={18}  className="hover:fill-[#ED0423] fill-[#3799CE] ml-auto cursor-pointer"
                      onClick={openedVisitesActivesDelete}   />
                      </Group>
                    </Card.Section>
                  </Card>
                  <Modal opened={VisitesActivesDeleteOpened} onClose={closeVisitesActivesDelete} withCloseButton={false}>
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                  Êtes-vous sûr de vouloir supprimer l&apos;événement ?

                </Alert>
                <Group justify="space-between" mt="md" mb="xs">
                <Button
                variant="filled"
                size="xs"
                radius="lg"
                color="red"
                onClick={() => {
                  // Use the event's actual ID, not counterId
                  const eventId = Number(visit.id);
                  if (!isNaN(eventId)) {
                    deleteVisits(eventId);
                    closeVisitesActivesDelete();
                  } else {
                    console.error("Invalid event ID:", visit.id);
                    notifications.show({
                      title: 'Error',
                      message: 'Could not delete event: Invalid ID',
                      color: 'red',
                    });
                  }
                }}
              >
                Oui
              </Button>
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="lime.4"
                    onClick={closeVisitesActivesDelete}
                  >
                    Non
                  </Button>
                </Group>
            </Modal>
                    </>
                  </div>
                )}
              </Draggable>
            ))
          ) : (
            <Text>No results found</Text>
          )}
                {provided.placeholder}
              </div>
              </SimpleBar>
              </div>
            )}
          </Droppable>
        </DragDropContext>
        ) : (
          <Text>No results found</Text>
        )}
        {provided.placeholder}
      </div>
    )}
  </Droppable>
  </DragDropContext>
                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 hover:bg-[#3799CE]/90"
                  >
                    Add New Event
                  </Button>
                </div>
              </Container>
        </Tabs.Panel>
        {/* <Tabs.Panel value="3">Third tab content</Tabs.Panel> */}
      </Tabs>
          </div>
            </div>
          </div>
          </DragDropContext>
            </SimpleBar>
             {/* Add Patient Modal */}
             <AjouterUnRendezVous
               opened={rendezVousOpened}
               onClose={closeRendezVous}
               appointmentForm={appointmentForm}
               handleSubmit={handleSubmit}
               eventTitle={eventTitle}
               setEventTitle={setEventTitle}
               titleOptions={titleOptions}
               setTitleOptions={setTitleOptions}
               newTitle={newTitle}
               setNewTitle={setNewTitle}
               patientName={patientName}
               setPatientName={setPatientName}
               patientlastName={patientlastName}
               setPatientlastName={setPatientlastName}
               openListDesPatient={openListDesPatient}
               eventDateDeNaissance={eventDateDeNaissance}
               handleDateChange={handleDateChange}
               eventAge={eventAge}
               genderOption={genderOption}
               handleOptionChange={handleOptionChange}
               eventEtatCivil={eventEtatCivil}
               setEventEtatCivil={setEventEtatCivil}
               eventCin={eventCin}
               setEventCin={setEventCin}
               address={address}
               setAddress={setAddress}
               eventTelephone={eventTelephone}
               setEventTelephone={setEventTelephone}
               email={email}
               setEmail={setEmail}
               patientdoctor={patientdoctor}
               setPatientDocteur={setPatientDocteur}
               patientsocialSecurity={patientsocialSecurity}
               setSocialSecurity={setSocialSecurity}
               consultationTypes={consultationTypes}
               setConsultationTypes={setConsultationTypes}
               patienttypeConsultation={patienttypeConsultation}
               setPatientTypeConsultation={setPatientTypeConsultation}
               setEventType={setEventType}
               searchValue={searchValue}
               setSearchValue={setSearchValue}
               dureeDeLexamen={dureeDeLexamen}
               getEventTypeColor={getEventTypeColor}
               newConsultationType={newConsultationType}
               setNewConsultationType={setNewConsultationType}
               newConsultationColor={newConsultationColor}
               setNewConsultationColor={setNewConsultationColor}
               ColorPickeropened={ColorPickeropened}
               openedColorPicker={openedColorPicker}
               closeColorPicker={closeColorPicker}
               changeEndValue={changeEndValue}
               setChangeEndValue={setChangeEndValue}
               setDureeDeLexamen={setDureeDeLexamen}
               eventAganda={eventAganda}
               setEventAganda={setEventAganda}
               agendaTypes={agendaTypes}
               setAgendaTypes={setAgendaTypes}
               newAgendaType={newAgendaType}
               setNewAgendaType={setNewAgendaType}
               isWaitingList={isWaitingList}
               eventDate={eventDate}
               setEventDate={setEventDate}
               eventTime={eventTime}
               setEventTime={setEventTime}
               eventConsultation={eventConsultation}
               openListRendezVous={openListRendezVous}
               ListRendezVousOpened={ListRendezVousOpened}
               closeListRendezVous={closeListRendezVous}
               patientcomment={patientcomment}
               setPatientcomment={setPatientcomment}
               patientnotes={patientnotes}
               setPatientNotes={setPatientNotes}
               patientcommentairelistedattente={patientcommentairelistedattente}
               setPatientCommentairelistedattente={setPatientCommentairelistedattente}
               eventResourceId={eventResourceId}
               setEventResourceId={setEventResourceId}
               eventType={eventType}
               checkedAppelvideo={checkedAppelvideo}
               handleAppelvideoChange={handleAppelvideoChange}
               checkedRappelSms={checkedRappelSms}
               handleRappelSmsChange={handleRappelSmsChange}
               checkedRappelEmail={checkedRappelEmail}
               handleRappelEmailChange={handleRappelEmailChange}
               currentPatient={currentPatient}
               waitingList={waitingList}
               setWaitingList={setWaitingList}
               setPatientModalOpen={setPatientModalOpen}
               notifications={notifications}
             />            
               {/* Add to Waiting List Modal - Now using AjouterUnRendezVous component */}
               <AjouterUnRendezVous
                 opened={AddwaitingListOpened}
                 onClose={closeAddwaitingList}
                 appointmentForm={appointmentForm}
                 handleSubmit={handleAddPatient}
                 eventTitle={eventTitle}
                 setEventTitle={setEventTitle}
                 titleOptions={titleOptions}
                 setTitleOptions={setTitleOptions}
                 newTitle={newTitle}
                 setNewTitle={setNewTitle}
                 patientName={patientName}
                 setPatientName={setPatientName}
                 patientlastName={patientlastName}
                 setPatientlastName={setPatientlastName}
                 openListDesPatient={openListDesPatient}
                 eventDateDeNaissance={eventDateDeNaissance}
                 handleDateChange={handleDateChange}
                 eventAge={eventAge}
                 genderOption={genderOption}
                 handleOptionChange={handleOptionChange}
                 eventEtatCivil={eventEtatCivil}
                 setEventEtatCivil={setEventEtatCivil}
                 eventCin={eventCin}
                 setEventCin={setEventCin}
                 address={address}
                 setAddress={setAddress}
                 eventTelephone={eventTelephone}
                 setEventTelephone={setEventTelephone}
                 email={email}
                 setEmail={setEmail}
                 patientdoctor={patientdoctor}
                 setPatientDocteur={setPatientDocteur}
                 patientsocialSecurity={patientsocialSecurity}
                 setSocialSecurity={setSocialSecurity}
                 consultationTypes={consultationTypes}
                 setConsultationTypes={setConsultationTypes}
                 patienttypeConsultation={patienttypeConsultation}
                 setPatientTypeConsultation={setPatientTypeConsultation}
                 setEventType={setEventType}
                 searchValue={searchValue}
                 setSearchValue={setSearchValue}
                 dureeDeLexamen={dureeDeLexamen}
                 getEventTypeColor={getEventTypeColor}
                 newConsultationType={newConsultationType}
                 setNewConsultationType={setNewConsultationType}
                 newConsultationColor={newConsultationColor}
                 setNewConsultationColor={setNewConsultationColor}
                 ColorPickeropened={ColorPickeropened}
                 openedColorPicker={openedColorPicker}
                 closeColorPicker={closeColorPicker}
                 changeEndValue={changeEndValue}
                 setChangeEndValue={setChangeEndValue}
                 setDureeDeLexamen={setDureeDeLexamen}
                 eventAganda={eventAganda}
                 setEventAganda={setEventAganda}
                 agendaTypes={agendaTypes}
                 setAgendaTypes={setAgendaTypes}
                 newAgendaType={newAgendaType}
                 setNewAgendaType={setNewAgendaType}
                 isWaitingList={true}
                 eventDate={eventDate}
                 setEventDate={setEventDate}
                 eventTime={eventTime}
                 setEventTime={setEventTime}
                 eventConsultation={eventConsultation}
                 openListRendezVous={openListRendezVous}
                 ListRendezVousOpened={ListRendezVousOpened}
                 closeListRendezVous={closeListRendezVous}
                 patientcomment={patientcomment}
                 setPatientcomment={setPatientcomment}
                 patientnotes={patientnotes}
                 setPatientNotes={setPatientNotes}
                 patientcommentairelistedattente={patientcommentairelistedattente}
                 setPatientCommentairelistedattente={setPatientCommentairelistedattente}
                 eventResourceId={eventResourceId}
                 setEventResourceId={setEventResourceId}
                 eventType={eventType}
                 checkedAppelvideo={checkedAppelvideo}
                 handleAppelvideoChange={handleAppelvideoChange}
                 checkedRappelSms={checkedRappelSms}
                 handleRappelSmsChange={handleRappelSmsChange}
                 checkedRappelEmail={checkedRappelEmail}
                 handleRappelEmailChange={handleRappelEmailChange}
                 currentPatient={currentPatient}
                 waitingList={waitingList}
                 setWaitingList={setWaitingList}
                 setPatientModalOpen={setPatientModalOpen}
                 notifications={notifications}
                 // New props for Edit Modal
                 showEditModal={false}
                 setShowEditModal={() => {}}
                 selectedEvent={null}
                 setSelectedEvent={() => {}}
                 resetForm={resetForm}
                 handleEditSubmit={() => {}}
                 closeRendezVous={closeAddwaitingList}
                 initialConsultationTypes={initialConsultationTypes}
               />


              {/* Edit Waiting List Modal - Now using AjouterUnRendezVous component */}
              <AjouterUnRendezVous
                opened={EditwaitingListOpened}
                onClose={closeEditwaitingList}
                appointmentForm={appointmentForm}
                handleSubmit={handleUpdatePatient}
                eventTitle={eventTitle}
                setEventTitle={setEventTitle}
                titleOptions={titleOptions}
                setTitleOptions={setTitleOptions}
                newTitle={newTitle}
                setNewTitle={setNewTitle}
                patientName={patientName}
                setPatientName={setPatientName}
                patientlastName={patientlastName}
                setPatientlastName={setPatientlastName}
                openListDesPatient={openListDesPatient}
                eventDateDeNaissance={eventDateDeNaissance}
                handleDateChange={handleDateChange}
                eventAge={eventAge}
                genderOption={genderOption}
                handleOptionChange={handleOptionChange}
                eventEtatCivil={eventEtatCivil}
                setEventEtatCivil={setEventEtatCivil}
                eventCin={eventCin}
                setEventCin={setEventCin}
                address={address}
                setAddress={setAddress}
                eventTelephone={eventTelephone}
                setEventTelephone={setEventTelephone}
                email={email}
                setEmail={setEmail}
                patientdoctor={patientdoctor}
                setPatientDocteur={setPatientDocteur}
                patientsocialSecurity={patientsocialSecurity}
                setSocialSecurity={setSocialSecurity}
                consultationTypes={consultationTypes}
                setConsultationTypes={setConsultationTypes}
                patienttypeConsultation={patienttypeConsultation}
                setPatientTypeConsultation={setPatientTypeConsultation}
                setEventType={setEventType}
                searchValue={searchValue}
                setSearchValue={setSearchValue}
                dureeDeLexamen={dureeDeLexamen}
                getEventTypeColor={getEventTypeColor}
                newConsultationType={newConsultationType}
                setNewConsultationType={setNewConsultationType}
                newConsultationColor={newConsultationColor}
                setNewConsultationColor={setNewConsultationColor}
                ColorPickeropened={ColorPickeropened}
                openedColorPicker={openedColorPicker}
                closeColorPicker={closeColorPicker}
                changeEndValue={changeEndValue}
                setChangeEndValue={setChangeEndValue}
                setDureeDeLexamen={setDureeDeLexamen}
                eventAganda={eventAganda}
                setEventAganda={setEventAganda}
                agendaTypes={agendaTypes}
                setAgendaTypes={setAgendaTypes}
                newAgendaType={newAgendaType}
                setNewAgendaType={setNewAgendaType}
                isWaitingList={false}
                eventDate={eventDate}
                setEventDate={setEventDate}
                eventTime={eventTime}
                setEventTime={setEventTime}
                eventConsultation={eventConsultation}
                openListRendezVous={openListRendezVous}
                ListRendezVousOpened={ListRendezVousOpened}
                closeListRendezVous={closeListRendezVous}
                patientcomment={patientcomment}
                setPatientcomment={setPatientcomment}
                patientnotes={patientnotes}
                setPatientNotes={setPatientNotes}
                patientcommentairelistedattente={patientcommentairelistedattente}
                setPatientCommentairelistedattente={setPatientCommentairelistedattente}
                eventResourceId={eventResourceId}
                setEventResourceId={setEventResourceId}
                eventType={eventType}
                checkedAppelvideo={checkedAppelvideo}
                handleAppelvideoChange={handleAppelvideoChange}
                checkedRappelSms={checkedRappelSms}
                handleRappelSmsChange={handleRappelSmsChange}
                checkedRappelEmail={checkedRappelEmail}
                handleRappelEmailChange={handleRappelEmailChange}
                currentPatient={currentPatient}
                waitingList={waitingList}
                setWaitingList={setWaitingList}
                setPatientModalOpen={setPatientModalOpen}
                notifications={notifications}
                // New props for Edit Modal
                showEditModal={false}
                setShowEditModal={() => {}}
                selectedEvent={null}
                setSelectedEvent={() => {}}
                resetForm={resetForm}
                handleEditSubmit={() => {}}
                closeRendezVous={closeEditwaitingList}
                initialConsultationTypes={initialConsultationTypes}
              />


              {/*   Show Patient List */}
             <Modal.Root opened={ListDesPatientOpened} onClose={closeListDesPatient}   size="100%">
            <PatientList/>
             </Modal.Root>

               
                <InfoModal
                  opened={infoModalOpen}
                  onClose={() => setInfoModalOpen(false)}
                  viewPatient={viewPatient}
                  color={color}
                  eventConsultation={eventConsultation}
                  eventResourceId={eventResourceId}
                />
                      <PatientDetailsModal
                        opened={showViewModal}
                        onClose={() => setShowViewModal(false)}
                        selectedEvent={selectedEvent}
                        eventResourceId={eventResourceId}
                      />
            {/* Edit Patient Modal - Now using AjouterUnRendezVous component */}
            <AjouterUnRendezVous
              opened={showEditModal}
              onClose={() => {
                resetForm();
                setShowEditModal(false);
              }}
              appointmentForm={appointmentForm}
              handleSubmit={handleEditSubmit}
              eventTitle={eventTitle}
              setEventTitle={setEventTitle}
              titleOptions={titleOptions}
              setTitleOptions={setTitleOptions}
              newTitle={newTitle}
              setNewTitle={setNewTitle}
              patientName={patientName}
              setPatientName={setPatientName}
              patientlastName={patientlastName}
              setPatientlastName={setPatientlastName}
              openListDesPatient={openListDesPatient}
              eventDateDeNaissance={eventDateDeNaissance}
              handleDateChange={handleDateChange}
              eventAge={eventAge}
              genderOption={genderOption}
              handleOptionChange={handleOptionChange}
              eventEtatCivil={eventEtatCivil}
              setEventEtatCivil={setEventEtatCivil}
              eventCin={eventCin}
              setEventCin={setEventCin}
              address={address}
              setAddress={setAddress}
              eventTelephone={eventTelephone}
              setEventTelephone={setEventTelephone}
              email={email}
              setEmail={setEmail}
              patientdoctor={patientdoctor}
              setPatientDocteur={setPatientDocteur}
              patientsocialSecurity={patientsocialSecurity}
              setSocialSecurity={setSocialSecurity}
              consultationTypes={consultationTypes}
              setConsultationTypes={setConsultationTypes}
              patienttypeConsultation={patienttypeConsultation}
              setPatientTypeConsultation={setPatientTypeConsultation}
              setEventType={setEventType}
              searchValue={searchValue}
              setSearchValue={setSearchValue}
              dureeDeLexamen={dureeDeLexamen}
              getEventTypeColor={getEventTypeColor}
              newConsultationType={newConsultationType}
              setNewConsultationType={setNewConsultationType}
              newConsultationColor={newConsultationColor}
              setNewConsultationColor={setNewConsultationColor}
              ColorPickeropened={ColorPickeropened}
              openedColorPicker={openedColorPicker}
              closeColorPicker={closeColorPicker}
              changeEndValue={changeEndValue}
              setChangeEndValue={setChangeEndValue}
              setDureeDeLexamen={setDureeDeLexamen}
              eventAganda={eventAganda}
              setEventAganda={setEventAganda}
              agendaTypes={agendaTypes}
              setAgendaTypes={setAgendaTypes}
              newAgendaType={newAgendaType}
              setNewAgendaType={setNewAgendaType}
              isWaitingList={false}
              eventDate={eventDate}
              setEventDate={setEventDate}
              eventTime={eventTime}
              setEventTime={setEventTime}
              eventConsultation={eventConsultation}
              openListRendezVous={openListRendezVous}
              ListRendezVousOpened={ListRendezVousOpened}
              closeListRendezVous={closeListRendezVous}
              patientcomment={patientcomment}
              setPatientcomment={setPatientcomment}
              patientnotes={patientnotes}
              setPatientNotes={setPatientNotes}
              patientcommentairelistedattente={patientcommentairelistedattente}
              setPatientCommentairelistedattente={setPatientCommentairelistedattente}
              eventResourceId={eventResourceId}
              setEventResourceId={setEventResourceId}
              eventType={eventType}
              checkedAppelvideo={checkedAppelvideo}
              handleAppelvideoChange={handleAppelvideoChange}
              checkedRappelSms={checkedRappelSms}
              handleRappelSmsChange={handleRappelSmsChange}
              checkedRappelEmail={checkedRappelEmail}
              handleRappelEmailChange={handleRappelEmailChange}
              currentPatient={currentPatient}
              waitingList={waitingList}
              setWaitingList={setWaitingList}
              setPatientModalOpen={setPatientModalOpen}
              notifications={notifications}
              // New props for Edit Modal
              showEditModal={showEditModal}
              setShowEditModal={setShowEditModal}
              selectedEvent={selectedEvent}
              setSelectedEvent={setSelectedEvent}
              resetForm={resetForm}
              handleEditSubmit={handleEditSubmit}
              closeRendezVous={closeRendezVous}
              initialConsultationTypes={initialConsultationTypes}
            />

      {/* Edit Patient Modal */}
       <Modal
         opened={rescheduleModalOpen}
         onClose={() => {
           setRescheduleModalOpen(false);
           setAppointmentToReschedule(null);
         }}
         title="Reschedule Appointment"
       >
         {appointmentToReschedule && (
           <form onSubmit={(e) => {
             e.preventDefault();
             const newDateTime = new Date(appointmentForm.values.rescheduleDateTime);
             const duration = appointmentToReschedule.end.getTime() - appointmentToReschedule.start.getTime();

             const updatedAppointment = {
               ...appointmentToReschedule,
               start: newDateTime,
               end: new Date(newDateTime.getTime() + duration),
             };

             setAppointments(appointments.map(app =>
               app.id === appointmentToReschedule.id ? updatedAppointment : app
             ));

             notifications.show({
               title: 'Appointment Rescheduled',
               message: `${updatedAppointment.title}'s appointment has been rescheduled`,
               color: 'blue',
             });

             setRescheduleModalOpen(false);
             setAppointmentToReschedule(null);
             appointmentForm.reset();
           }}>
             <TextInput
               label="Current Date/Time"
               value={moment(appointmentToReschedule.start).format('MMMM D, YYYY h:mm A')}
               disabled
             />

             <input
               type="datetime-local"
               {...appointmentForm.getInputProps('rescheduleDateTime')}
               style={{
                 width: '100%',
                 padding: '10px',
                 marginTop: '10px',
                 marginBottom: '20px',
                 borderRadius: '4px',
                 border: '1px solid #ced4da',
               }}
               required
             />

             <Group justify="flex-end" mt="md">
               <Button type="submit">
                 Reschedule
               </Button>
             </Group>
           </form>
         )}
       </Modal>
       {/* Start Fiche patient */}
        <Modal.Root
        opened={Fichepatientpened}
        onClose={closeFichepatient}
        fullScreen
        radius={0}
        transitionProps={{ transition: 'fade', duration: 200 }}
      >
        <Modal.Content  className="overflow-y-hidden ">
           <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px",  }}>
            <Modal.Title>
              <Group justify="space-between" gap="sm">
              <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24"
                >
                  <g
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  >
                    <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
                    <circle cx={16} cy={16} r={6}></circle>
                  </g>
                </svg>
                Fiche patient
              </Text>
              <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
              patient name
              </p>
              </Group>
            </Modal.Title>
            <Modal.CloseButton className="mantine-focus-always text-[var(--mantine-color-white)] hover:text-[#868e96]" />
          </Modal.Header>
          <Modal.Body style={{ padding: '0px',}}>
          <div className="py-2 pl-4 h-[600px]">
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
            <div className=" pr-4">
       <PatientRecord/>
       </div>
       </SimpleBar>
       </div>
       </Modal.Body>
       </Modal.Content>
      </Modal.Root>
      {/* <AlertsButton />
     <AlertsModal
        opened={alertsOpened}
        onClose={() => setAlertsOpened(false)}
      /> */}
	    
      </>
    );
};

export default CetteJournee;