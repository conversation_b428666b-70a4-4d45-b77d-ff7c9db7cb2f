import React, { useState } from 'react';
import {
  TextInput,
  Select,
  Radio,
  Group,
  Text,
  Button,
  Box,
  Divider,
  Grid,
  Avatar,
  Flex
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import {
  IconId,
  IconPhone,
  IconMapPin,
  IconBuildingHospital,
  IconNotes
} from '@tabler/icons-react';
import { IconCamera } from '@tabler/icons-react';
import { FileButton, Image, Switch } from '@mantine/core';

export interface PatientFormData {
  patientImage: File | null;
  title?: string;
  firstName?: string;
  lastName?: string;
  birthDate?: string;
  gender?: 'M' | 'F' | 'E';
  ethnicity?: string;
  fatherName?: string;
  motherName?: string;
  idNumber?: string;
  phone?: string;
  maritalStatus?: string;
  profession?: string;
  primaryDoctor?: string;
  referredBy?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  country?: string;
  city?: string;
  prefecture?: string;
  address?: string;
  comments?: string;
  paperFileNumber?: string;
  pricing?: string;
  isFavorite?: boolean;
  isInsured?: boolean;
  needsCompletion?: boolean;
  isDeceased?: boolean;
}

interface PatientInfoFormProps {
  onSubmit: (data: PatientFormData) => void;
  onCancel: () => void;
}

const PatientInfoForm: React.FC<PatientInfoFormProps> = ({ onSubmit, onCancel }) => {
  const [gender, setGender] = useState<'M' | 'F' | 'E' |null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Form submission logic
    onSubmit({
      // Include patientImage in the form data
      patientImage,
      // Add other form data here
    });
  };

  // Add this to your component where state variables are defined
const [patientImage, setPatientImage] = useState<File | null>(null);
const [imagePreview, setImagePreview] = useState<string | null>(null);

// Add this function to your component
const handleImageUpload = (file: File | null) => {
  if (file) {
    setPatientImage(file);
    const imageUrl = URL.createObjectURL(file);
    setImagePreview(imageUrl);
  }
};

  return (
    <>

    <Box className="bg-white rounded-md shadow-sm">
      {/* <Box className="bg-[#3799CE] p-3 flex items-center gap-2">
        <Avatar color="blue" radius="sm">
          <IconUser size={18} />
        </Avatar>
       <Text fw={600} c="white" className="text-lg">Fiche patient</Text>
      </Box> */}

      <form onSubmit={handleSubmit}>
      <Grid>
      <Grid.Col span={1}>
    <div className="relative h-[108px] w-[108px]">
    {imagePreview ? (
    <Image
      src={imagePreview}
      alt="Patient"
      className="w-full h-full object-cover"
    />
  ) : (
    <Avatar
          src="http://localhost:3000/41c7f30d-1607-4462-ac56-9e080e73934e"
          size={94}
          radius="md"
          />
  )}

  <div className="absolute bottom-0 left-0 ">
  <FileButton
    onChange={handleImageUpload}
    accept="image/png,image/jpeg,image/jpg"
  >
    {(props) => (
      <Box
        {...props}
        className=" cursor-pointer hover:bg-opacity-70 transition-all"
      >
       <IconCamera size={25} color="white" />
      </Box>
    )}
  </FileButton>

  </div>
</div>
      </Grid.Col>
      <Grid.Col span={2} pl={18}>

      <TextInput
                label="N° de dossier papier"
                placeholder=""
                className="mb-3"
              />

        <Select
          label="Tarification"
          placeholder="Sélectionner"
          data={[
            { value: 'standard', label: 'Standard' },
            { value: 'premium', label: 'Premium' }
          ]}
          className="mb-3"
        />
        <Switch
          //checked={checkedAppelvideo}
        // onChange={handleAppelvideoChange}
          color="teal"
          size="xs"
          label="Favoris"
      //thumbIcon={
          // checkedAppelvideo ? (
          // <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
          // ) : (
          // null
          // )
        //   }
          />
          <Switch
        // checked={checkedRappelSms}
        // onChange={handleRappelSmsChange}
          color="teal"
          size="xs"
          label="Assuré"
          className='mt-4'
          />
      </Grid.Col>
      <Grid.Col span={9}>
      <div className="pb-2">
     <Grid>
     <Grid.Col span={4}>
          <Select
                label="Titre"
                placeholder="Sélectionner"
                data={[
                  { value: 'mr', label: 'M.' },
                  { value: 'mme', label: 'Mme' },
                  { value: 'dr', label: 'Dr.' }
                ]}
                className="mb-3 "
              />
              </Grid.Col>
            <Grid.Col span={4}>
              <TextInput
                label="Nom"
                placeholder=""
                required
                className="mb-3 "
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <TextInput
                label="Prénom"
                placeholder=""
                required
                className="mb-3"
              />
            </Grid.Col>
        </Grid>
            <Grid>
              <Grid.Col span={3}>
                <DateInput
                  label="Date de naissance"
                  placeholder="JJ/MM/AAAA"
                  valueFormat="DD/MM/YYYY"
                  className="mb-3"
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <TextInput
                  label="Age"
                  placeholder=""
                  readOnly
                  className="mb-3"
                />
              </Grid.Col>
              <Grid.Col span={3}>
              <Select
                label="Origine ethnique"
                placeholder="Origine ethnique"
                data={[
                  { value: 'mr', label: 'M.' },
                  { value: 'mme', label: 'Mme' },
                  { value: 'dr', label: 'Dr.' }
                ]}
                className="mb-3 "
              />
              </Grid.Col>
            <Grid.Col span={3}>
              <Box className="mb-3">
                <Text size="sm" className="mb-1">Sexe</Text>
                <Radio.Group value={gender || ''} onChange={(val) => setGender(val as 'M' | 'F' | 'E')}>
                  <Group>
                    <Radio value="M" label="M" />
                    <Radio value="F" label="F" />
                    <Radio value="E" label="E" />
                  </Group>
                </Radio.Group>
              </Box>
            </Grid.Col>
          </Grid>

        <Grid>
          <Grid.Col span={6}>
            <TextInput
              label="Nom du père"
              placeholder=""
              className="mb-3"
            />
          </Grid.Col>
          <Grid.Col span={6}>
            <TextInput
              label="Nom de la mère"
              placeholder=""
              className="mb-3"
            />
          </Grid.Col>
        </Grid>

        <Grid>
          <Grid.Col span={6}>
            <TextInput
              label="CIN"
              placeholder=""
              className="mb-3"
            // icon={<IconId size={16} />}
              leftSection={<IconId size={16}  />}
            />
          </Grid.Col>
          <Grid.Col span={6}>
            <TextInput
              label="Téléphone"
              placeholder=""
              className="mb-3"
              //icon={<IconPhone size={16} />}
              leftSection={<IconPhone size={16}  />}
            />
          </Grid.Col>
        </Grid>

        <Grid>
          <Grid.Col span={6}>
            <Select
              label="Etat civil"
              placeholder="Célibataire"
              data={[
                { value: 'single', label: 'Célibataire' },
                { value: 'married', label: 'Marié' },
                { value: 'divorced', label: 'Divorcé' },
                { value: 'widowed', label: 'Veuf/Veuve' }
              ]}
              className="mb-3"
            />
          </Grid.Col>
          <Grid.Col span={6}>
            <Select
              label="Profession"
              placeholder="Employé(e)"
              data={[
                { value: 'employee', label: 'Employé(e)' },
                { value: 'self-employed', label: 'Indépendant' },
                { value: 'retired', label: 'Retraité' },
                { value: 'student', label: 'Étudiant' }
              ]}
              className="mb-3"
            />
          </Grid.Col>
    </Grid>

    <Grid>
      <Grid.Col span={6}>
        <Select
          label="Médecin traitant"
          placeholder="Sélectionner"
          data={[
            { value: 'dr1', label: 'Dr. Ahmed' },
            { value: 'dr2', label: 'Dr. Karim' },
            { value: 'dr3', label: 'Dr. Fatima' }
          ]}
          className="mb-3"
         //icon={<IconBuildingHospital size={16} />}
          leftSection={<IconBuildingHospital size={16}  />}
        />
      </Grid.Col>
      <Grid.Col span={6}>
        <TextInput
          label="Adressé par"
          placeholder=""
          className="mb-3"
        />
      </Grid.Col>
    </Grid>

    <Grid>
      <Grid.Col span={6}>
        <TextInput
          label="Contact d'urgence"
          placeholder=""
          className="mb-3"
        />
      </Grid.Col>
      <Grid.Col span={6}>
        <TextInput
          label="Téléphone"
          placeholder=""
          className="mb-3"
          //icon={<IconPhone size={16} />}
          leftSection={<IconPhone size={16}  />}
        />
      </Grid.Col>
    </Grid>

    <Divider my="sm" />

    <Grid>
      <Grid.Col span={4}>
        <Select
          label="Pays"
          placeholder="MAROC"
          data={[
            { value: 'maroc', label: 'MAROC' },
            { value: 'france', label: 'FRANCE' },
            { value: 'espagne', label: 'ESPAGNE' }
          ]}
          className="mb-3"
        />
      </Grid.Col>
      <Grid.Col span={4}>
        <Select
          label="Ville"
          placeholder="CASABLANCA"
          data={[
            { value: 'casablanca', label: 'CASABLANCA' },
            { value: 'rabat', label: 'RABAT' },
            { value: 'marrakech', label: 'MARRAKECH' }
          ]}
          className="mb-3"
        />
      </Grid.Col>
      <Grid.Col span={4}>
        <Select
          label="Préfecture"
          placeholder="Sélectionner"
          data={[
            { value: 'pref1', label: 'Préfecture 1' },
            { value: 'pref2', label: 'Préfecture 2' },
            { value: 'pref3', label: 'Préfecture 3' }
          ]}
          className="mb-3"
        />
      </Grid.Col>
    </Grid>

    <Grid>
      <Grid.Col span={6}>
        <TextInput
          label="Adresse"
          placeholder=""
          className="mb-3"
          //icon={<IconMapPin size={16} />}
          leftSection={<IconMapPin size={16}  />}
        />
      </Grid.Col>
      <Grid.Col span={6}>
        <TextInput
          label="Commentaire"
          placeholder=""
          className="mb-3"
          //icon={<IconNotes size={16} />}
          leftSection={<IconNotes size={16}  />}
        />
      </Grid.Col>
    </Grid>

    <Box className="mt-4">
      <Group>
    <Switch
          //checked={checkedAppelvideo}
        // onChange={handleAppelvideoChange}
          color="teal"
          size="xs"
          // label=" À compléter..."
      //thumbIcon={
          // checkedAppelvideo ? (
          // <IconNotes size={12} color="var(--mantine-color-teal-6)" stroke={3} />
          // ) : (
          // null
          // )
        //   }
          />
      <Text className="text-gray-600 flex items-center gap-2 mb-2">

        <IconNotes size={16} />
        À compléter...
      </Text>
      </Group>
      <Group>
      <Switch
          //checked={checkedAppelvideo}
        // onChange={handleAppelvideoChange}
          color="teal"
          size="xs"
          // label=" À compléter..."
      //thumbIcon={
          // checkedAppelvideo ? (
          // <IconNotes size={12} color="var(--mantine-color-teal-6)" stroke={3} />
          // ) : (
          // null
          // )
        //   }
          />
      <Text className="text-gray-600 flex items-center gap-2">

        <IconNotes size={16} />
        Décès
      </Text>
      </Group>
    </Box>
     </div>
      </Grid.Col>
    </Grid>

        <Flex justify="flex-end" gap="md" className="bg-gray-100 p-3">
          <Button variant="outline" color="gray" onClick={onCancel}>
            Annuler
          </Button>
          <Button type="submit" color="red">
            Enregistrer et quitter
          </Button>
          <Button type="button" color="gray">
            Enregistrer la fiche
          </Button>
        </Flex>
      </form>
    </Box>
    </>
  );
};

export default PatientInfoForm;