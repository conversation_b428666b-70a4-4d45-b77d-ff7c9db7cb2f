{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "app\\(auth)\\login\\page.tsx -> ~/components/SwitchColorMode": {"id": "app\\(auth)\\login\\page.tsx -> ~/components/SwitchColorMode", "files": ["static/chunks/_app-pages-browser_src_components_SwitchColorMode_tsx.js"]}, "layout\\navBarButton\\NavBarButton.tsx -> ./Iconbar/SwitchColorModes": {"id": "layout\\navBarButton\\NavBarButton.tsx -> ./Iconbar/SwitchColorModes", "files": ["static/chunks/_app-pages-browser_src_layout_navBarButton_Iconbar_SwitchColorModes_tsx.js"]}}