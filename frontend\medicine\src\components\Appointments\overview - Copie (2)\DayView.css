/* DayView.css */
.rbc-toolbar {
  height: 84px;
}
/* topheader */
.rbc-time-view {
  background-color: var(--content-background);
  color: var(--text-daisy);
  border: 0px solid var(--rbc-border) !important;
}
/* DayView.css */
.rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;

  position: relative;
  padding-top: 15px;
  /* overflow-y: auto; */
}
/* *********************************** //////////////////////////////////////////////////////////////*/
.rbc-time-view .rbc-time-gutter {
  width: 83px;
  text-align: right;
  white-space: nowrap;
  position: relative;
}
.rbc-time-column {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}
.rbc-time-column .rbc-timeslot-group,
.rbc-timeslot-group {
  flex: 1 1;
  display: flex;
  flex-flow: column nowrap;
  min-height: 40px;
  position: relative;
  border-bottom: 1px dashed var(--rbc-border) !important;
  background-color: var(--content-background) !important;
}
.rbc-time-content > * + * > * {
  border-left: 1px solid var(--rbc-border) !important;
}
.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid var(--rbc-border) !important;
}

.rbc-time-slot .rbc-label {
  display: flex;
  flex-direction: column;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  font-size: 0.625rem;
  font-family: Rubik, sans-serif;
  height: 20px;
  width: 47px;
  margin: 0px auto;
  border-radius: 8px;
  position: relative;
  top: -10px !important;
  background-color: var(--content-background);
}
.rbc-time-slot {
  color: #74a4c3;
}

/* .rbc-day-slot .rbc-events-container:after {
  background: url(/add.svg) center center / 16px 16px no-repeat !important;
} */

/* تحسين التصميم للخط الحالي */
.rbc-day-slot .rbc-timeslot-group .current-time {
  position: relative;
  height: 40px;
}

@media screen and (min-width: 767.98px) {
  .rbc-day-slot .rbc-timeslot-group .current-time .time-indicator {
    left: -84px !important;
    width: calc(100% + 84px) !important;
  }
}

.rbc-timeslot-group .current-time .time-indicator,
.rbc-timeslot-group .current-time .time-indicator .label {
  position: absolute;
}

.rbc-day-slot .rbc-timeslot-group .current-time .time-indicator .label {
  left: 8px;
}

@media screen and (min-width: 767.98px) {
  .rbc-day-slot .rbc-timeslot-group .current-time .time-indicator .label {
    left: 18px;
  }
}

.rbc-timeslot-group .current-time .time-indicator .label {
  background-color: #06b1bd; /* استخدام لون HEX مختصر */
  top: -50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  font-family: Rubik, sans-serif;
  height: 20px;
  width: 47px;
  margin: 0 auto;
  border-radius: 8px;
  color: #fff; /* استخدام لون HEX مختصر */
  box-shadow: 0 1px 8px rgba(38, 98, 240, 0.4);
}
.rbc-day-slot .rbc-timeslot-group .current-time .time-indicator {
  position: absolute;
  z-index: 50;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #06b1bd;
  pointer-events: none;
  width: 100%;
  transition: top 0.3s ease;
  top: 50%;
  transform: translateY(-50%);
  /* top: 14%;
  display: block;
  height: 1px;
  background-color: #06b1bd;
  position: absolute;
  z-index: 10;
  left: 0;
  width: 100%;
   */
}
.rbc-current-time-indicator {
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  height: 0px;
  background-color: #06b1bd;
  pointer-events: none;
}
/* .rbc-time-slot:not(.rbc-today .rbc-time-slot) {
  background-color: #fff;
} */
.rbc-time-slot {
  color: #74a4c3;
  z-index: 1;
}

.rbc-event,
.rbc-background-event {
  z-index: 2;
  /* background-color: red; */
  font-size: 13.2px;
}
.rbc-day-slot .rbc-event-content {
  word-wrap: break-word;
  flex: 1 1;
  height: 100%;
  line-height: 1;
  min-height: 1em;
  width: 100%;
}
.rbc-event-content {
  backface-visibility: visible;
}
   .rbc-time-content > * + * > * {
  background-image: url("/add.svg");
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position-x: center;
  background-position-y: center;
}   
 /* DayView.css */
 .rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;
  position: relative;
   padding-top: 15px !important; 
  /* overflow-y: auto; */
}
.rbc-toolbar {
  /* height: 84px; */
   display: none; 
}


/* ////////////////////////////////////////////// */
/* .event-content {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}*/
/* .event-content.expanded {
  z-index: 9999 !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}  */
.event-content {
  height: 100%;
  transition: all 0.3s ease-in-out;

}  
.description {
  margin-top: 8px;
  transition: all 0.3s ease-in-out;
  transform-origin: top;
} 

.description.hidden {
  opacity: 0;
  transform: scaleY(0);
  height: 0;
  margin: 0;
}

.description.visible {
  opacity: 1;
  transform: scaleY(1);
  height: auto;

 /* position: absolute;
  --popover-shadow: var(--mantine-shadow-md);
  transition-property: opacity;
  transition-duration: 150ms;
  transition-timing-function: ease;
  opacity: 1;
  z-index: 300;
  top: 273.3px;
  left: 660.4px;
  width: calc(12.5rem* var(--mantine-scale));  */
}

/* Override react-big-calendar's default styles */
.rbc-event {
  height: auto !important;
  max-height: none !important;
}

.rbc-event-content {
  height: auto !important;
}

/* .rbc-header {
  background-color: #f3f4f6;
  padding: 8px;
  font-weight: bold;
} */

/* .rbc-header span {
  display: block;
  text-align: center;
} */
/* .rbc-time-view .rbc-time-header {
  display: none;
} */
.rbc-time-view .rbc-time-header {
    display: flex;  
}
.rbc-time-header-content > .rbc-row.rbc-row-resource {
  border-bottom: 0px solid #ddd; 
}
.rbc-header {
  background-color: none !important;
   border-bottom: 0px solid #ddd !important;
}
.rbc-time-view-resources .rbc-time-gutter,
.rbc-time-view-resources .rbc-time-header-gutter {
  background-color: var(--header-nav-base) !important;
  border-right: 1px solid var(--border-color) !important;
 
}
.rbc-time-header-content {
  border-left: 1px solid var(--border-color) !important;
}
.rbc-header {
  background-color: var(--header-nav-base) !important;
  
}
.rbc-addons-dnd-resizable{
  width: 100% !important;
}
/* .rbc-time-column .rbc-timeslot-group, .rbc-timeslot-group {
  flex: 1 1;
  display: flex;
  flex-flow: column nowrap;
  // min-height: 56px; 
  min-height: 26.6px;
  position: relative;
  border-bottom: 1px dashed var(--rbc-border) !important;
  background-color: var(--content-background) !important;
} */
.rbc-time-column .rbc-timeslot-group, .rbc-timeslot-group {
  flex: 1 1;
  display: flex;
  flex-flow: column nowrap;
  /* min-height: 56px; */
  min-height: 26.6px;
  position: relative;
  border-bottom: 1px dashed var(--rbc-border) !important;
  background-color: var(--content-background) !important;
  
}


