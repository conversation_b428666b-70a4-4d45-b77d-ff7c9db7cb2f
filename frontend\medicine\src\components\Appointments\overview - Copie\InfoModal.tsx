import React from 'react';
import {
  Mo<PERSON>,
  Card,
  List,
  Text,
  Flex,
  Divider
} from '@mantine/core';
import { FaUserTie } from "react-icons/fa";
import { BsFillCalendar2DateFill } from "react-icons/bs";
import { IoArrowForwardOutline } from "react-icons/io5";
import moment from 'moment';
import { Patient } from '@/types/typesCalendarPatient';

interface InfoModalProps {
  opened: boolean;
  onClose: () => void;
  viewPatient: Patient | null;
  color: string;
  eventConsultation: string;
  eventResourceId: number;
}

const InfoModal: React.FC<InfoModalProps> = ({
  opened,
  onClose,
  viewPatient,
  color,
  eventConsultation,
  eventResourceId
}) => {
  if (!viewPatient) return null;

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Patient Information"
      size="lg"
    >
      <Card
        shadow="sm"
        padding="lg"
        radius="md"
        withBorder
        style={{ border: `calc(0.0625rem * var(--mantine-scale)) solid ${color}` }}
      >
        <Text size="sm" mb="md">patientId: {viewPatient.id}</Text>

        <List size="sm" withPadding className="capitalize" type="ordered">
          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Event Title: </b>{viewPatient.eventType}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Name: </b>{viewPatient.first_name}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <BsFillCalendar2DateFill color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Prenom: </b>{viewPatient.last_name}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Date De Naissance: </b>{viewPatient.birth_date}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Age: </b>{viewPatient.age}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Phone Numbers: </b>{viewPatient.phone_numbers}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Gender: </b>{viewPatient.gender}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Docteur: </b>{viewPatient.docteur}</Text>
            <Text><b>Event Title: </b>{viewPatient.event_Title}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Sociale: </b>{viewPatient.socialSecurity}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Type Consultation: </b>{viewPatient.typeConsultation}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Consultation: </b>{eventConsultation}min</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Agenda: </b>{viewPatient.etatAganda}</Text>
            <Text><b>Room: </b>{eventResourceId}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Date: </b>{viewPatient.date}</Text>
            <Text><b>Time: </b>{moment(viewPatient.start).format('HH:mm')} - {moment(viewPatient.end).format('HH:mm')}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Commentaire: </b>{viewPatient.comment}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Resource Id: </b>{eventResourceId}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Address: </b>{viewPatient.address}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Etat Civil: </b>{viewPatient.etatCivil}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Email: </b>{viewPatient.email}</Text>
          </List.Item>

          <List.Item
            icon={
              <Flex mih={10} gap="md" justify="flex-start" align="flex-start" direction="row" wrap="wrap">
                <FaUserTie color={"#3799CE"} />
                <IoArrowForwardOutline size={16} color={color} />
              </Flex>
            }
          >
            <Text><b>Commentaire Liste d&apos;Attente: </b>{viewPatient.commentairelistedattente}</Text>
            <Text><b>CIN: </b>{viewPatient.cin}</Text>
            <Text><b>Consultation Duration: </b>{viewPatient.consultationDuration} min</Text>
            <Text><b>Notes: </b>{viewPatient.notes}</Text>
          </List.Item>

          <Divider my="sm" />
        </List>

        {viewPatient.notes && (
          <>
            <Text mt="md" fw={500}>Notes:</Text>
            <Text>{viewPatient.notes}</Text>
          </>
        )}

        {viewPatient.lastVisit && (
          <>
            <Text mt="md" fw={500}>Last Visit:</Text>
            <Text><b>Date:</b> {moment(viewPatient.lastVisit.date).format('MMM DD, YYYY')}</Text>
            <Text><b>Notes:</b> {viewPatient.lastVisit.notes}</Text>
          </>
        )}
      </Card>
    </Modal>
  );
};

export default InfoModal;
