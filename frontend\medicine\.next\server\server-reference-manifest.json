{"node": {"40323d0e69dba655eb007a8c0cf59da73e0aef9e8c": {"workers": {"app/(dashboard)/web/page": {"moduleId": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csorry%5C%5CDesktop%5C%5Csvp%5C%5Cfrontend%5C%5Cmedicine%5C%5Csrc%5C%5Cactions%5C%5Cswitch-locale.ts%22%2C%5B%7B%22id%22%3A%2240323d0e69dba655eb007a8c0cf59da73e0aef9e8c%22%2C%22exportedName%22%3A%22switchLocaleAction%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}, "app/(dashboard)/[specialty]/page": {"moduleId": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csorry%5C%5CDesktop%5C%5Csvp%5C%5Cfrontend%5C%5Cmedicine%5C%5Csrc%5C%5Cactions%5C%5Cswitch-locale.ts%22%2C%5B%7B%22id%22%3A%2240323d0e69dba655eb007a8c0cf59da73e0aef9e8c%22%2C%22exportedName%22%3A%22switchLocaleAction%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}, "app/(auth)/login/page": {"moduleId": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csorry%5C%5CDesktop%5C%5Csvp%5C%5Cfrontend%5C%5Cmedicine%5C%5Csrc%5C%5Cactions%5C%5Cswitch-locale.ts%22%2C%5B%7B%22id%22%3A%2240323d0e69dba655eb007a8c0cf59da73e0aef9e8c%22%2C%22exportedName%22%3A%22switchLocaleAction%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}, "app/(dashboard)/dashboard/page": {"moduleId": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csorry%5C%5CDesktop%5C%5Csvp%5C%5Cfrontend%5C%5Cmedicine%5C%5Csrc%5C%5Cactions%5C%5Cswitch-locale.ts%22%2C%5B%7B%22id%22%3A%2240323d0e69dba655eb007a8c0cf59da73e0aef9e8c%22%2C%22exportedName%22%3A%22switchLocaleAction%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}}, "layer": {"app/(dashboard)/web/page": "action-browser", "app/(dashboard)/[specialty]/page": "action-browser", "app/(auth)/login/page": "action-browser", "app/(dashboard)/dashboard/page": "action-browser"}}}, "edge": {}, "encryptionKey": "rHGA/vFHARN3uRFe+JXE/tyC4+bQU1t80ZuHmunTnPM="}